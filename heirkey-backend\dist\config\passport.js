"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const passport_1 = __importDefault(require("passport"));
const passport_google_oauth20_1 = require("passport-google-oauth20");
const User_1 = __importDefault(require("../models/User"));
// Set up Passport with GoogleStrategy for authentication
passport_1.default.use(new passport_google_oauth20_1.Strategy({
    clientID: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    callbackURL: `${process.env.BACKEND_URL}/v1/auth/google/callback`,
    passReqToCallback: true,
    proxy: true // Enable proxy support for production
}, (req, accessToken, refreshToken, profile, done) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    try {
        const email = (_a = profile.emails) === null || _a === void 0 ? void 0 : _a[0].value;
        if (!email) {
            return done(null, false, { message: 'Email not provided from Google' });
        }
        // Get the state from the request query parameters
        const state = req.query.state || 'login';
        // Check if user exists
        const existingUser = yield User_1.default.findOne({
            $or: [
                { googleId: profile.id },
                { email: email }
            ]
        });
        if (state === 'login') {
            // Login flow
            if (!existingUser) {
                return done(null, false, {
                    message: 'No account found with this email. Please sign up first.',
                    redirectTo: 'register',
                    state: 'login'
                });
            }
            // Update Google ID if not set
            if (!existingUser.googleId) {
                existingUser.googleId = profile.id;
                existingUser.image = ((_b = profile.photos) === null || _b === void 0 ? void 0 : _b[0].value) || existingUser.image;
                existingUser.externalUser = true;
                yield existingUser.save();
            }
            return done(null, existingUser);
        }
        else {
            // Signup flow
            if (existingUser) {
                return done(null, false, {
                    message: 'User already exists. Please log in.',
                    redirectTo: 'login'
                });
            }
            // Create new user
            const newUser = yield new User_1.default({
                googleId: profile.id,
                email: email,
                username: profile.displayName,
                firstName: ((_c = profile.name) === null || _c === void 0 ? void 0 : _c.givenName) || '',
                lastName: ((_d = profile.name) === null || _d === void 0 ? void 0 : _d.familyName) || '',
                image: ((_e = profile.photos) === null || _e === void 0 ? void 0 : _e[0].value) || '',
                externalUser: true
            }).save();
            return done(null, newUser);
        }
    }
    catch (err) {
        return done(err, false);
    }
})));
// Serialize user into session
passport_1.default.serializeUser((user, done) => {
    done(null, user.id);
});
// Deserialize user from session
passport_1.default.deserializeUser((id, done) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield User_1.default.findById(id);
        done(null, user);
    }
    catch (err) {
        done(err, null);
    }
}));
//# sourceMappingURL=passport.js.map