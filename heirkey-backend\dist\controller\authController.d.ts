import { NextFunction, Request, Response } from 'express';
import { AuthRequest } from '../middleware/authMiddleware';
export declare const registerUser: (req: Request, res: Response) => Promise<void>;
export declare const verifyEmail: (req: Request, res: Response) => Promise<void>;
export declare const resendVerificationOTP: (req: Request, res: Response) => Promise<void>;
export declare const loginUser: (req: Request, res: Response) => Promise<void>;
export declare const getUserProfile: (req: AuthRequest, res: Response) => Promise<void>;
export declare const updateUserProfile: (req: AuthRequest, res: Response) => Promise<void>;
export declare const updateUserProfileImage: (req: AuthRequest, res: Response) => Promise<void>;
export declare const forgetPassword: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const resetPassword: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getAllUsers: (_req: Request, res: Response) => Promise<void>;
export declare const userDetails: (req: Request, res: Response) => Promise<void>;
export declare const getProfile: (req: Request, res: Response) => void;
export declare const logout: (req: Request & {
    user?: {
        userId: string;
    };
}, res: Response) => Promise<void>;
export declare const getVerificationStatus: (req: Request, res: Response) => Promise<void>;
