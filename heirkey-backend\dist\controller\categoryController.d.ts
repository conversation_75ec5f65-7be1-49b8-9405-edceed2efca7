import { Request, Response } from 'express';
import Category from '../models/category';
export declare const category: (req: Request, res: Response) => Promise<void>;
export declare const getAllCategories: (req: Request, res: Response) => Promise<void>;
export declare const getCategories: (req: Request, res: Response) => Promise<void>;
export declare const getFullCategoryDataQuestions: (req: Request, res: Response) => Promise<void>;
export declare const ensureDefaultCategories: (req: Request, res: Response) => Promise<void>;
export { Category };
