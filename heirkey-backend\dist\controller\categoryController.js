"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Category = exports.ensureDefaultCategories = exports.getFullCategoryDataQuestions = exports.getCategories = exports.getAllCategories = exports.category = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const category_1 = __importDefault(require("../models/category"));
exports.Category = category_1.default;
const category = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name } = req.body;
        if (!name) {
            res.status(400).json({ message: 'Category name is required.' });
            return;
        }
        const category = new category_1.default({ name });
        yield category.save();
        res.status(201).json(category);
    }
    catch (error) {
        res.status(500).json({ message: 'Error creating category', error });
    }
});
exports.category = category;
const getAllCategories = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const categories = yield category_1.default.find();
        res.status(200).json(categories);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching categories', error });
    }
});
exports.getAllCategories = getAllCategories;
const getCategories = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const categories = yield category_1.default.aggregate([
            {
                $lookup: {
                    from: 'subcategories',
                    localField: '_id',
                    foreignField: 'categoryId',
                    as: 'subcategories'
                }
            }
        ]);
        res.status(200).json(categories);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching categories', error });
    }
});
exports.getCategories = getCategories;
const getFullCategoryDataQuestions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { categoryId } = req.query;
        if (!categoryId || typeof categoryId !== 'string') {
            res.status(400).json({ message: 'Valid categoryId is required' });
            return;
        }
        console.log(`Received request for category with ID: ${categoryId}`);
        // Try multiple strategies to find the category
        let category;
        let query;
        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose_1.default.Types.ObjectId.isValid(categoryId)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                category = yield category_1.default.findById(categoryId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    query = { _id: new mongoose_1.default.Types.ObjectId(categoryId) };
                }
            }
            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = yield category_1.default.findOne({
                    name: new RegExp(categoryId, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    query = { _id: category._id };
                }
            }
            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId, 10);
                category = yield category_1.default.findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    query = { _id: category._id };
                }
                else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = yield category_1.default.find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            query = { _id: category._id };
                        }
                    }
                }
            }
        }
        catch (error) {
            console.error('Error finding category:', error);
        }
        if (!category) {
            console.log(`Category not found for ID: ${categoryId}`);
            res.status(404).json({ message: 'Category not found' });
            return;
        }
        const result = yield category_1.default.aggregate([
            {
                $match: query || {} // Provide empty object as fallback
            },
            {
                $lookup: {
                    from: 'subcategories',
                    let: { categoryId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$categoryId', '$$categoryId'] }
                            }
                        },
                        {
                            $lookup: {
                                from: 'questions',
                                let: { subCategoryId: '$_id' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $eq: ['$subCategoryId', '$$subCategoryId'] }
                                        }
                                    }
                                ],
                                as: 'questions'
                            }
                        }
                    ],
                    as: 'subcategories'
                }
            }
        ]);
        if (result.length === 0) {
            res.status(404).json({ message: 'Category data not found' });
            return;
        }
        res.status(200).json(result[0]);
    }
    catch (error) {
        console.error('Error in getCategoriesWithSubcategoriesAndQuestions:', error);
        res.status(500).json({
            message: 'Error fetching categories with subcategories and questions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getFullCategoryDataQuestions = getFullCategoryDataQuestions;
// Utility endpoint to ensure categories exist
const ensureDefaultCategories = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Define default categories
        const defaultCategories = [
            { name: 'Home Instructions', numericId: 1 },
            { name: 'Home Documents', numericId: 2 },
            { name: 'Will Location', numericId: 3 },
            { name: 'Funeral Arrangements', numericId: 4 },
            { name: 'Important Contacts', numericId: 5 },
            { name: 'Social Media and Phone', numericId: 6 },
        ];
        // Check if categories already exist
        const existingCategories = yield category_1.default.find();
        if (existingCategories.length === 0) {
            // Create categories if none exist
            const createdCategories = yield category_1.default.insertMany(defaultCategories);
            console.log(`Created ${createdCategories.length} default categories`);
            // Create default subcategories for Home Instructions
            const homeInstructionsCategory = createdCategories.find(c => c.numericId === 1);
            if (homeInstructionsCategory) {
                const subcategories = [
                    { name: 'Pets', description: 'Information about your pets', categoryId: homeInstructionsCategory._id },
                    { name: 'Trash', description: 'Trash pickup information', categoryId: homeInstructionsCategory._id },
                    { name: 'Other', description: 'Other home instructions', categoryId: homeInstructionsCategory._id },
                    { name: 'Security', description: 'Home security information', categoryId: homeInstructionsCategory._id },
                ];
                const Subcategory = mongoose_1.default.model('Subcategory');
                yield Subcategory.insertMany(subcategories);
                console.log(`Created default subcategories for Home Instructions`);
            }
            res.status(201).json({
                message: 'Default categories created successfully',
                categories: createdCategories
            });
        }
        else {
            // Categories already exist
            res.status(200).json({
                message: 'Categories already exist',
                categories: existingCategories
            });
        }
    }
    catch (error) {
        console.error('Error ensuring default categories:', error);
        res.status(500).json({
            message: 'Error ensuring default categories',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.ensureDefaultCategories = ensureDefaultCategories;
//# sourceMappingURL=categoryController.js.map