"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.profileDetails = exports.googleAuthFailure = exports.googleAuthCallback = exports.googleAuth = void 0;
const passport_1 = __importDefault(require("passport"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// Google authentication route
exports.googleAuth = passport_1.default.authenticate('google', {
    scope: ['profile', 'email']
});
// Handle the callback after Google has authenticated the user
const googleAuthCallback = (req, res) => {
    if (!req.user) {
        const authInfo = req.authInfo;
        const error = (authInfo === null || authInfo === void 0 ? void 0 : authInfo.message) || 'Authentication failed';
        const redirectTo = (authInfo === null || authInfo === void 0 ? void 0 : authInfo.redirectTo) || 'login';
        const state = (authInfo === null || authInfo === void 0 ? void 0 : authInfo.state) || 'login';
        if (state === 'login' && redirectTo === 'register') {
            return res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(error)}&redirectTo=register`);
        }
        return res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(error)}&redirectTo=${redirectTo}`);
    }
    // Successful authentication
    const user = req.user;
    // Create token with both id and userId for compatibility
    const token = jsonwebtoken_1.default.sign({
        id: user._id,
        userId: user._id, // Include both formats for compatibility
        email: user.email
    }, process.env.JWT_SECRET || 'default', { expiresIn: '24h' });
    console.log('Creating token for user:', user._id);
    // Prepare user data for frontend
    const userData = {
        id: user._id,
        _id: user._id, // Include both formats for compatibility
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        image: user.image,
        externalUser: true,
        isNewUser: !user.firstName || !user.lastName
    };
    // Redirect to frontend with token and user data
    // Make sure to properly encode the token and user data
    const encodedToken = encodeURIComponent(token);
    const encodedUserData = encodeURIComponent(JSON.stringify(userData));
    console.log('Google Auth Callback: Redirecting to frontend with token and user data');
    console.log('Token length:', token.length);
    console.log('User data:', userData);
    const redirectUrl = `${process.env.FRONTEND_URL}/auth/google/callback?success=true&token=${encodedToken}&user=${encodedUserData}`;
    res.redirect(redirectUrl);
};
exports.googleAuthCallback = googleAuthCallback;
// Error handling for authentication failure
const googleAuthFailure = (req, res) => {
    const error = 'Authentication failed. Please try again.';
    res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(error)}`);
};
exports.googleAuthFailure = googleAuthFailure;
// Profile details handler
const profileDetails = (req, res) => {
    try {
        const user = req.user;
        if (!user) {
            res.status(401).json({ message: 'Unauthorized: User not found in request' });
            return;
        }
        res.status(200).json({ user: user, message: 'Profile retrieved successfully' });
    }
    catch (error) {
        console.error('Error in profileDetails:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};
exports.profileDetails = profileDetails;
//# sourceMappingURL=googleAuth.js.map