"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.acceptInvitation = exports.getInvitations = exports.inviteUser = void 0;
const crypto_1 = __importDefault(require("crypto"));
const User_1 = __importDefault(require("../models/User"));
const Owner_1 = __importDefault(require("../models/Owner"));
const Role_1 = __importDefault(require("../models/Role"));
const InvitedUser_1 = __importDefault(require("../models/InvitedUser"));
const InvitedUser_2 = require("../types/InvitedUser");
const Role_2 = require("../types/Role");
const email_1 = __importDefault(require("../utils/email"));
const inviteUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { name, email, relation, phone } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        // Find the owner making the invitation
        const owner = yield Owner_1.default.findOne({ userId });
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }
        // Check if user with this email already exists
        const existingUser = yield User_1.default.findOne({ email: email.toLowerCase() });
        if (existingUser) {
            // Check if there's already a pending invitation for this user from this owner
            const existingInvitation = yield InvitedUser_1.default.findOne({
                ownerId: owner._id,
                invitedUserId: existingUser._id,
                status: InvitedUser_2.InvitationStatus.PENDING
            });
            if (existingInvitation) {
                res.status(400).json({ message: 'Invitation already sent to this email' });
                return;
            }
            else {
                res.status(400).json({ message: 'User with this email already exists' });
                return;
            }
        }
        // Generate invitation token
        const invitationToken = crypto_1.default.randomBytes(32).toString('hex');
        const hashedToken = crypto_1.default.createHash('sha256').update(invitationToken).digest('hex');
        // Create user record first (without password)
        const newUser = new User_1.default({
            firstName: name.split(' ')[0],
            lastName: name.split(' ').slice(1).join(' ') || '',
            email: email.toLowerCase(),
            phone,
            ownerId: owner._id, // Set owner reference for invited users
            externalUser: false
        });
        yield newUser.save();
        // Create invitation record
        const invitation = new InvitedUser_1.default({
            ownerId: owner._id,
            invitedUserId: newUser._id,
            relation,
            status: InvitedUser_2.InvitationStatus.PENDING,
            invitationToken: hashedToken,
            invitationTokenExpire: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        });
        yield invitation.save();
        // Send invitation email
        const invitationUrl = `${process.env.FRONTEND_URL}/accept-invitation/${invitationToken}`;
        const emailMessage = `
            <h2>You've been invited to join Heirkey</h2>
            <p>Hello ${name},</p>
            <p>You have been invited by ${owner.getFullName()} to join Heirkey as a ${relation}.</p>
            <p>Click the link below to accept the invitation and set up your account:</p>
            <a href="${invitationUrl}" style="background-color: #4CAF50; color: white; padding: 14px 20px; text-decoration: none; display: inline-block; border-radius: 4px;">Accept Invitation</a>
            <p>This invitation will expire in 24 hours.</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
        `;
        yield (0, email_1.default)({
            to: email,
            subject: 'Invitation to join Heirkey',
            html: emailMessage
        });
        res.status(201).json({
            message: 'Invitation sent successfully',
            invitation: {
                id: invitation._id,
                email,
                relation,
                status: invitation.status,
                createdAt: invitation.createdAt
            },
            // For testing purposes - remove in production
            debugToken: invitationToken
        });
    }
    catch (error) {
        console.error('Invitation error:', error);
        res.status(500).json({
            message: 'Error sending invitation',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.inviteUser = inviteUser;
const getInvitations = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        // Find the owner
        const owner = yield Owner_1.default.findOne({ userId });
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }
        // Get all invitations sent by this owner
        const invitations = yield InvitedUser_1.default.findByOwnerId(owner._id);
        res.status(200).json({
            message: 'Invitations retrieved successfully',
            invitations: invitations.map(inv => {
                var _a, _b, _c;
                return ({
                    id: inv._id,
                    email: (_a = inv.invitedUser) === null || _a === void 0 ? void 0 : _a.email,
                    name: `${((_b = inv.invitedUser) === null || _b === void 0 ? void 0 : _b.firstName) || ''} ${((_c = inv.invitedUser) === null || _c === void 0 ? void 0 : _c.lastName) || ''}`.trim(),
                    relation: inv.relation,
                    status: inv.status,
                    createdAt: inv.createdAt,
                    updatedAt: inv.updatedAt
                });
            })
        });
    }
    catch (error) {
        console.error('Get invitations error:', error);
        res.status(500).json({
            message: 'Error retrieving invitations',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getInvitations = getInvitations;
const acceptInvitation = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { token, password } = req.body;
        // Hash the token to match stored version
        const hashedToken = crypto_1.default.createHash('sha256').update(token).digest('hex');
        // Find invitation by token
        const invitation = yield InvitedUser_1.default.findOne({
            invitationToken: hashedToken,
            invitationTokenExpire: { $gt: new Date() },
            status: InvitedUser_2.InvitationStatus.PENDING
        }).populate('invitedUser').populate('owner');
        if (!invitation) {
            res.status(400).json({ message: 'Invalid or expired invitation token' });
            return;
        }
        const user = invitation.invitedUser;
        const owner = invitation.owner;
        // Determine role based on relation
        let roleType;
        const relationLower = invitation.relation.toLowerCase();
        if (relationLower.includes('family') || relationLower.includes('spouse') || relationLower.includes('child')) {
            roleType = Role_2.RoleType.FAMILY;
        }
        else {
            roleType = Role_2.RoleType.NOMINEE;
        }
        // Find the appropriate role
        const role = yield Role_1.default.findOne({ name: roleType });
        if (!role) {
            res.status(500).json({ message: `${roleType} role not found` });
            return;
        }
        // Update user with password and role
        user.password = password;
        user.roleId = role._id;
        yield user.save();
        // Update invitation status
        invitation.status = InvitedUser_2.InvitationStatus.ACCEPTED;
        invitation.invitationToken = undefined;
        invitation.invitationTokenExpire = undefined;
        yield invitation.save();
        res.status(200).json({
            message: 'Invitation accepted successfully',
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: roleType,
                relation: invitation.relation
            }
        });
    }
    catch (error) {
        console.error('Accept invitation error:', error);
        res.status(500).json({
            message: 'Error accepting invitation',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.acceptInvitation = acceptInvitation;
//# sourceMappingURL=invitationController.js.map