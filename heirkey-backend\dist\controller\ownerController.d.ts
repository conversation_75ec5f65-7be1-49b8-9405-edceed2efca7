import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/authMiddleware';
export declare const getAllOwners: (req: Request, res: Response) => Promise<void>;
export declare const getOwnerById: (req: Request, res: Response) => Promise<void>;
export declare const getOwnerByUserId: (req: Request, res: Response) => Promise<void>;
export declare const getMyOwnerProfile: (req: AuthRequest, res: Response) => Promise<void>;
export declare const updateOwner: (req: Request, res: Response) => Promise<void>;
export declare const updateMyOwnerProfile: (req: AuthRequest, res: Response) => Promise<void>;
export declare const deleteOwner: (req: Request, res: Response) => Promise<void>;
export declare const getOwnerByEmail: (req: Request, res: Response) => Promise<void>;
