"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveAnswers = exports.getQuestionsBySubCategory = exports.getQuestionsByCategory = exports.deleteQuestion = exports.updateQuestion = exports.getQuestionById = exports.getQuestions = exports.createQuestion = void 0;
const Question_1 = require("../models/Question");
const userInput_1 = __importDefault(require("../models/userInput"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
const createQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const question = yield Question_1.Question.create(req.body);
        res.status(201).json(question);
    }
    catch (error) {
        res.status(400).json({ message: error.message });
    }
});
exports.createQuestion = createQuestion;
const getQuestions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log(req.query);
        const questions = yield Question_1.Question.find();
        res.status(200).json(questions);
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(500).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'An unknown error occurred' });
        }
    }
});
exports.getQuestions = getQuestions;
const getQuestionById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const question = yield Question_1.Question.findById(req.params.id);
        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }
        res.status(200).json(question);
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(500).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'An unknown error occurred' });
        }
    }
});
exports.getQuestionById = getQuestionById;
const updateQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const question = yield Question_1.Question.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }
        res.status(200).json(question);
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ error: error.message });
        }
        else {
            res.status(400).json({ error: 'An unknown error occurred' });
        }
    }
});
exports.updateQuestion = updateQuestion;
const deleteQuestion = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const question = yield Question_1.Question.findByIdAndDelete(req.params.id);
        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }
        res.status(200).json({ message: 'Question deleted successfully' });
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(500).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'An unknown error occurred' });
        }
    }
});
exports.deleteQuestion = deleteQuestion;
const getQuestionsByCategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { categoryId } = req.params;
        if (!categoryId) {
            return res.status(400).json({ error: 'Category ID is required' });
        }
        console.log(`Received request for questions with categoryId: ${categoryId}`);
        // Try to find the category using multiple strategies
        let category;
        let query;
        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose_1.default.Types.ObjectId.isValid(categoryId)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                const objId = new mongoose_1.default.Types.ObjectId(categoryId);
                category = yield mongoose_1.default.model('Category').findById(objId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    query = { categoryId: objId };
                }
            }
            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = yield mongoose_1.default.model('Category').findOne({
                    name: new RegExp(categoryId, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    query = { categoryId: category._id };
                }
            }
            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId, 10);
                category = yield mongoose_1.default.model('Category').findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    query = { categoryId: category._id };
                }
                else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = yield mongoose_1.default.model('Category').find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            query = { categoryId: category._id };
                        }
                    }
                }
            }
        }
        catch (error) {
            console.error('Error finding category:', error);
        }
        if (!query) {
            console.log(`Category not found for ID: ${categoryId}`);
            return res.status(404).json({ error: 'Category not found' });
        }
        const questions = yield Question_1.Question.find(query);
        res.status(200).json(questions);
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(500).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'An unknown error occurred' });
        }
    }
});
exports.getQuestionsByCategory = getQuestionsByCategory;
const getQuestionsBySubCategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { subCategoryId } = req.params;
        if (!subCategoryId) {
            return res.status(400).json({ error: 'Subcategory ID is required' });
        }
        console.log(`Received request for questions with subCategoryId: ${subCategoryId}`);
        // Try to find the subcategory using multiple strategies
        let subcategory;
        let query;
        try {
            // Strategy 1: If subCategoryId is a valid ObjectId, use it directly
            if (mongoose_1.default.Types.ObjectId.isValid(subCategoryId)) {
                console.log(`Trying to find subcategory by ObjectId: ${subCategoryId}`);
                const objId = new mongoose_1.default.Types.ObjectId(subCategoryId);
                subcategory = yield mongoose_1.default.model('Subcategory').findById(objId);
                if (subcategory) {
                    console.log(`Found subcategory by ObjectId: ${subcategory.name}`);
                    query = { subCategoryId: objId };
                }
            }
            // Strategy 2: If not found by ObjectId, try to find by name
            if (!subcategory) {
                console.log(`Trying to find subcategory by name containing: ${subCategoryId}`);
                subcategory = yield mongoose_1.default.model('Subcategory').findOne({
                    name: new RegExp(subCategoryId, 'i')
                });
                if (subcategory) {
                    console.log(`Found subcategory by name: ${subcategory.name}`);
                    query = { subCategoryId: subcategory._id };
                }
            }
            // Strategy 3: If numeric ID, try to find by index position
            if (!subcategory && /^\d+$/.test(subCategoryId)) {
                console.log(`Trying to find subcategory by numeric index: ${subCategoryId}`);
                const numericId = parseInt(subCategoryId, 10);
                // Find all subcategories and get the one at the specified index (1-based)
                const allSubcategories = yield mongoose_1.default.model('Subcategory').find().sort({ createdAt: 1 });
                if (numericId > 0 && numericId <= allSubcategories.length) {
                    subcategory = allSubcategories[numericId - 1];
                    if (subcategory) {
                        console.log(`Found subcategory by index: ${subcategory.name}`);
                        query = { subCategoryId: subcategory._id };
                    }
                }
            }
        }
        catch (error) {
            console.error('Error finding subcategory:', error);
        }
        if (!query) {
            console.log(`Subcategory not found for ID: ${subCategoryId}`);
            return res.status(404).json({ error: 'Subcategory not found' });
        }
        const questions = yield Question_1.Question.find(query);
        res.status(200).json(questions);
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(500).json({ error: error.message });
        }
        else {
            res.status(500).json({ error: 'An unknown error occurred' });
        }
    }
});
exports.getQuestionsBySubCategory = getQuestionsBySubCategory;
const saveAnswers = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { categoryId, subCategoryId, answersBySection } = req.body;
        if (!categoryId || !subCategoryId || !answersBySection) {
            return res.status(400).json({
                error: 'Category ID, Subcategory ID, and answers are required'
            });
        }
        // Get user ID from auth middleware
        let userId;
        if (req.user) {
            // If user is authenticated via Passport (req.user is set)
            userId = req.user && ('_id' in req.user ? req.user._id : 'id' in req.user ? req.user.id : undefined);
            console.log('User authenticated via Passport:', userId);
        }
        else if (req.body.userId) {
            // If userId is provided in the request body
            userId = req.body.userId;
            console.log('User ID from request body:', userId);
        }
        else {
            console.log('Authentication failed. User object:', req.user);
            return res.status(401).json({ error: 'User not authenticated' });
        }
        console.log('Using user ID for saving answers:', userId);
        // Validate and convert IDs to ObjectId
        let userIdObj, categoryIdObj, subCategoryIdObj;
        try {
            // Validate userId
            if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
                console.error('Invalid userId format:', userId);
                return res.status(400).json({ error: 'Invalid user ID format' });
            }
            userIdObj = new mongoose_1.default.Types.ObjectId(userId);
            // For categoryId, we need to handle numeric IDs differently
            if (/^\d+$/.test(categoryId)) {
                // If it's a numeric ID, find the category by numericId
                console.log('Looking up category by numericId:', categoryId);
                const category = yield mongoose_1.default.model('Category').findOne({ numericId: parseInt(categoryId, 10) });
                if (!category) {
                    console.error('Category not found with numericId:', categoryId);
                    return res.status(404).json({ error: 'Category not found' });
                }
                categoryIdObj = category._id;
                console.log('Found category:', category.name, 'with ID:', categoryIdObj);
            }
            else if (mongoose_1.default.Types.ObjectId.isValid(categoryId)) {
                // If it's a valid ObjectId, use it directly
                categoryIdObj = new mongoose_1.default.Types.ObjectId(categoryId);
            }
            else {
                console.error('Invalid categoryId format:', categoryId);
                return res.status(400).json({ error: 'Invalid category ID format' });
            }
            // Validate subCategoryId
            if (!mongoose_1.default.Types.ObjectId.isValid(subCategoryId)) {
                console.error('Invalid subCategoryId format:', subCategoryId);
                return res.status(400).json({ error: 'Invalid subcategory ID format' });
            }
            subCategoryIdObj = new mongoose_1.default.Types.ObjectId(subCategoryId);
        }
        catch (error) {
            console.error('Error converting IDs to ObjectId:', error);
            return res.status(400).json({
                error: 'Invalid ID format',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
        console.log('Using ObjectIds for query:');
        console.log('- userId:', userIdObj);
        console.log('- categoryId:', categoryIdObj);
        console.log('- subCategoryId:', subCategoryIdObj);
        // Get the user to find their ownerId
        const user = yield User_1.default.findById(userIdObj);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        let ownerId = null;
        if (user.ownerId) {
            ownerId = user.ownerId;
            console.log('- ownerId:', ownerId);
        }
        else {
            console.log('- User does not have an associated owner, proceeding without ownerId');
        }
        // Check if user already has answers for this category and subcategory
        const existingUserInput = yield userInput_1.default.findOne({
            userId: userIdObj,
            categoryId: categoryIdObj,
            subCategoryId: subCategoryIdObj
        });
        if (existingUserInput) {
            // Update existing user input
            console.log('Updating existing user input');
            existingUserInput.answersBySection = answersBySection;
            // Also update ownerId if user has one and it was missing
            if (ownerId && !existingUserInput.ownerId) {
                existingUserInput.ownerId = ownerId;
            }
            yield existingUserInput.save();
            return res.status(200).json(existingUserInput);
        }
        // Create new user input
        console.log('Creating new user input');
        const userInputData = {
            userId: userIdObj,
            categoryId: categoryIdObj,
            subCategoryId: subCategoryIdObj,
            answersBySection
        };
        // Add ownerId if available
        if (ownerId) {
            userInputData.ownerId = ownerId;
        }
        const userInput = new userInput_1.default(userInputData);
        yield userInput.save();
        res.status(201).json(userInput);
    }
    catch (error) {
        console.error('Error saving answers:', error);
        // Check for specific MongoDB errors
        if (error instanceof Error) {
            const errorMessage = error.message;
            // Check for common MongoDB errors
            if (errorMessage.includes('Cast to ObjectId failed')) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    details: errorMessage,
                    message: 'One of the IDs provided is not in the correct format for MongoDB'
                });
            }
            else if (errorMessage.includes('validation failed')) {
                return res.status(400).json({
                    error: 'Validation error',
                    details: errorMessage,
                    message: 'The data provided did not pass validation'
                });
            }
            res.status(500).json({
                error: 'Error saving answers',
                details: errorMessage,
                message: 'There was a problem saving your answers'
            });
        }
        else {
            res.status(500).json({
                error: 'An unknown error occurred',
                message: 'There was an unexpected problem saving your answers'
            });
        }
    }
});
exports.saveAnswers = saveAnswers;
//# sourceMappingURL=questionController.js.map