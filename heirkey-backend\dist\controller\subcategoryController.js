"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSubcategoryDetailsQuestions = exports.SubcategoriesByCategoryId = exports.subcategory = void 0;
const subcategory_1 = __importDefault(require("../models/subcategory"));
const mongoose_1 = __importDefault(require("mongoose"));
const subcategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, categoryId } = req.body;
        if (!name || !categoryId) {
            res.status(400).json({ message: 'Subcategory name and category ID are required.' });
            return;
        }
        const subcategory = new subcategory_1.default({ name, categoryId });
        yield subcategory.save();
        res.status(201).json(subcategory);
    }
    catch (error) {
        res.status(500).json({ message: 'Error creating subcategory', error });
    }
});
exports.subcategory = subcategory;
const SubcategoriesByCategoryId = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { categoryId } = req.query;
        if (!categoryId) {
            res.status(400).json({ message: 'Category ID is required.' });
            return;
        }
        console.log(`Received request for subcategories with categoryId: ${categoryId}`);
        // Try to find the category using multiple strategies
        let category;
        let query;
        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose_1.default.Types.ObjectId.isValid(categoryId)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                const objId = new mongoose_1.default.Types.ObjectId(categoryId);
                category = yield mongoose_1.default.model('Category').findById(objId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    query = { categoryId: objId };
                }
            }
            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = yield mongoose_1.default.model('Category').findOne({
                    name: new RegExp(categoryId, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    query = { categoryId: category._id };
                }
            }
            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId, 10);
                category = yield mongoose_1.default.model('Category').findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    query = { categoryId: category._id };
                }
                else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = yield mongoose_1.default.model('Category').find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            query = { categoryId: category._id };
                        }
                    }
                }
            }
        }
        catch (error) {
            console.error('Error finding category:', error);
        }
        if (!query) {
            console.log(`Category not found for ID: ${categoryId}`);
            res.status(404).json({ message: 'Category not found' });
            return;
        }
        const subcategories = yield subcategory_1.default.find(query);
        res.json(subcategories);
    }
    catch (error) {
        console.error('Error fetching subcategories:', error);
        res.status(500).json({ message: 'Error fetching subcategories', error });
    }
});
exports.SubcategoriesByCategoryId = SubcategoriesByCategoryId;
const getSubcategoryDetailsQuestions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { categoryId } = req.query;
        if (!categoryId) {
            res.status(400).json({ message: 'Category ID is required.' });
            return;
        }
        console.log(`Received request for subcategories with categoryId: ${categoryId}`);
        // Try to find the category using multiple strategies
        let category;
        let matchQuery;
        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose_1.default.Types.ObjectId.isValid(categoryId)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                const objId = new mongoose_1.default.Types.ObjectId(categoryId);
                category = yield mongoose_1.default.model('Category').findById(objId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    matchQuery = { categoryId: objId };
                }
            }
            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = yield mongoose_1.default.model('Category').findOne({
                    name: new RegExp(categoryId, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    matchQuery = { categoryId: category._id };
                }
            }
            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId, 10);
                category = yield mongoose_1.default.model('Category').findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    matchQuery = { categoryId: category._id };
                }
                else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = yield mongoose_1.default.model('Category').find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            matchQuery = { categoryId: category._id };
                        }
                    }
                }
            }
        }
        catch (error) {
            console.error('Error finding category:', error);
        }
        if (!matchQuery) {
            console.log(`Category not found for ID: ${categoryId}`);
            res.status(404).json({ message: 'Category not found' });
            return;
        }
        const subcategories = yield subcategory_1.default.aggregate([
            {
                $match: matchQuery
            },
            {
                $lookup: {
                    from: 'questions',
                    localField: '_id',
                    foreignField: 'subCategoryId',
                    as: 'questions'
                }
            },
            {
                $addFields: {
                    questions: { $arrayElemAt: ['$questions', 0] }
                }
            },
        ]);
        res.status(200).json(subcategories);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching subcategories with questions', error });
    }
});
exports.getSubcategoryDetailsQuestions = getSubcategoryDetailsQuestions;
// export const getSubcategoriesWithQuestionsAndCategory = async (req: Request, res: Response): Promise<void> => {
//     try {
//         const { categoryId } = req.query;
//         if (!categoryId) {
//             res.status(400).json({ message: 'Category ID is required.' });
//             return;
//         }
//         const subcategories = await Subcategory.aggregate([
//             {
//                 $match: { categoryId: new mongoose.Types.ObjectId(categoryId as string) }
//             },
//             {
//                 $lookup: {
//                     from: 'questions',
//                     localField: '_id',
//                     foreignField: 'subCategoryId',
//                     as: 'questions'
//                 }
//             },
//             {
//                 $addFields: {
//                     questions: { $arrayElemAt: ['$questions', 0] }
//                 }
//             },
//         ]);
//         res.status(200).json(subcategories);
//     } catch (error) {
//         res.status(500).json({ message: 'Error fetching subcategories with questions and category details', error });
//     }
// };
//# sourceMappingURL=subcategoryController.js.map