import { Request, Response } from 'express';
export declare const createSubscription: (req: Request, res: Response) => Promise<void>;
export declare const getAllSubscriptions: (req: Request, res: Response) => Promise<void>;
export declare const getSubscriptionById: (req: Request, res: Response) => Promise<void>;
export declare const getOwnerSubscription: (req: Request, res: Response) => Promise<void>;
export declare const changePlan: (req: Request, res: Response) => Promise<void>;
export declare const cancelSubscription: (req: Request, res: Response) => Promise<void>;
export declare const getExpiredSubscriptions: (_req: Request, res: Response) => Promise<void>;
export declare const renewSubscription: (req: Request, res: Response) => Promise<void>;
