import { Request, Response, NextFunction } from 'express';
import { IUser } from '../types/User';
export interface AuthRequest extends Request {
    user?: IUser | any;
}
export declare const combinedAuth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const authMiddleware: (req: AuthRequest, res: Response, next: NextFunction) => void;
export declare const ensureAuth: (req: Request, res: Response, next: NextFunction) => void;
