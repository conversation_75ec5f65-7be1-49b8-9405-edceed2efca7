import { Request, Response, NextFunction } from 'express';
import { RoleType } from '../types/Role';
export interface AuthRequest extends Request {
    user?: any;
}
export declare const checkPermission: (permission: string) => (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const checkRole: (roleType: RoleType) => (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireOwner: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireOwnerOrNominee: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const attachUserRole: (req: AuthRequest, _res: Response, next: NextFunction) => Promise<void>;
