"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.attachUserRole = exports.requireOwnerOrNominee = exports.requireOwner = exports.checkRole = exports.checkPermission = void 0;
const Role_1 = __importDefault(require("../models/Role"));
const Role_2 = require("../types/Role");
// Middleware to check if user has specific permission
const checkPermission = (permission) => {
    return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            if (!req.user) {
                res.status(401).json({
                    status: 'fail',
                    message: 'Authentication required'
                });
                return;
            }
            // If user doesn't have a role, deny access
            if (!req.user.roleId) {
                res.status(403).json({
                    status: 'fail',
                    message: 'No role assigned to user'
                });
                return;
            }
            // Get user's role
            const role = yield Role_1.default.findById(req.user.roleId);
            if (!role || !role.isActive) {
                res.status(403).json({
                    status: 'fail',
                    message: 'Invalid or inactive role'
                });
                return;
            }
            // Check if role has the required permission
            if (!role.permissions.includes(permission)) {
                res.status(403).json({
                    status: 'fail',
                    message: `Access denied. Required permission: ${permission}`
                });
                return;
            }
            next();
        }
        catch (error) {
            res.status(500).json({
                status: 'error',
                message: 'Error checking permissions',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
};
exports.checkPermission = checkPermission;
// Middleware to check if user has specific role
const checkRole = (roleType) => {
    return (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            if (!req.user) {
                res.status(401).json({
                    status: 'fail',
                    message: 'Authentication required'
                });
                return;
            }
            // If user doesn't have a role, deny access
            if (!req.user.roleId) {
                res.status(403).json({
                    status: 'fail',
                    message: 'No role assigned to user'
                });
                return;
            }
            // Get user's role
            const role = yield Role_1.default.findById(req.user.roleId);
            if (!role || !role.isActive) {
                res.status(403).json({
                    status: 'fail',
                    message: 'Invalid or inactive role'
                });
                return;
            }
            // Check if user has the required role
            if (role.name !== roleType) {
                res.status(403).json({
                    status: 'fail',
                    message: `Access denied. Required role: ${roleType}`
                });
                return;
            }
            next();
        }
        catch (error) {
            res.status(500).json({
                status: 'error',
                message: 'Error checking role',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
};
exports.checkRole = checkRole;
// Middleware to check if user is Owner (highest privilege)
exports.requireOwner = (0, exports.checkRole)(Role_2.RoleType.OWNER);
// Middleware to check if user is Owner or Nominee
const requireOwnerOrNominee = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({
                status: 'fail',
                message: 'Authentication required'
            });
            return;
        }
        if (!req.user.roleId) {
            res.status(403).json({
                status: 'fail',
                message: 'No role assigned to user'
            });
            return;
        }
        const role = yield Role_1.default.findById(req.user.roleId);
        if (!role || !role.isActive) {
            res.status(403).json({
                status: 'fail',
                message: 'Invalid or inactive role'
            });
            return;
        }
        if (role.name !== Role_2.RoleType.OWNER && role.name !== Role_2.RoleType.NOMINEE) {
            res.status(403).json({
                status: 'fail',
                message: 'Access denied. Owner or Nominee role required'
            });
            return;
        }
        next();
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error checking role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.requireOwnerOrNominee = requireOwnerOrNominee;
// Middleware to attach user's role information to request
const attachUserRole = (req, _res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (req.user && req.user.roleId) {
            const role = yield Role_1.default.findById(req.user.roleId);
            if (role) {
                req.user.role = role;
            }
        }
        next();
    }
    catch (error) {
        // Don't fail the request if role attachment fails, just continue
        next();
    }
});
exports.attachUserRole = attachUserRole;
//# sourceMappingURL=roleMiddleware.js.map