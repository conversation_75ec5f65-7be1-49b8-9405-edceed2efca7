"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const crypto_1 = __importDefault(require("crypto"));
const InvitedUser_1 = require("../types/InvitedUser");
const invitedUserSchema = new mongoose_1.default.Schema({
    ownerId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Owner',
        required: true
    },
    invitedUserId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    status: {
        type: String,
        enum: Object.values(InvitedUser_1.InvitationStatus),
        default: InvitedUser_1.InvitationStatus.PENDING,
        required: true
    },
    relation: {
        type: String,
        required: true,
        trim: true
    },
    invitationToken: {
        type: String,
        default: null
    },
    invitationTokenExpire: {
        type: Date,
        default: null
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Add indexes for better query performance
invitedUserSchema.index({ ownerId: 1 });
invitedUserSchema.index({ invitedUserId: 1 });
invitedUserSchema.index({ status: 1 });
invitedUserSchema.index({ ownerId: 1, invitedUserId: 1 }, { unique: true }); // Prevent duplicate invitations
// Virtual to populate owner data
invitedUserSchema.virtual('owner', {
    ref: 'Owner',
    localField: 'ownerId',
    foreignField: '_id',
    justOne: true
});
// Virtual to populate invited user data
invitedUserSchema.virtual('invitedUser', {
    ref: 'User',
    localField: 'invitedUserId',
    foreignField: '_id',
    justOne: true
});
// Static method to find invitations by owner ID
invitedUserSchema.statics.findByOwnerId = function (ownerId) {
    return this.find({ ownerId }).populate('owner').populate('invitedUser');
};
// Static method to find invitations by user ID
invitedUserSchema.statics.findByUserId = function (userId) {
    return this.find({ invitedUserId: userId }).populate('owner').populate('invitedUser');
};
// Static method to find pending invitations
invitedUserSchema.statics.findPendingInvitations = function (ownerId) {
    const query = { status: InvitedUser_1.InvitationStatus.PENDING };
    if (ownerId) {
        query.ownerId = ownerId;
    }
    return this.find(query).populate('owner').populate('invitedUser');
};
// Instance method to generate invitation token
invitedUserSchema.methods.generateInvitationToken = function () {
    const token = crypto_1.default.randomBytes(32).toString('hex');
    const hashedToken = crypto_1.default.createHash('sha256').update(token).digest('hex');
    this.invitationToken = hashedToken;
    this.invitationTokenExpire = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    return token; // return the raw token (unhashed) to be sent via email
};
const InvitedUser = mongoose_1.default.model('InvitedUser', invitedUserSchema);
exports.default = InvitedUser;
//# sourceMappingURL=InvitedUser.js.map