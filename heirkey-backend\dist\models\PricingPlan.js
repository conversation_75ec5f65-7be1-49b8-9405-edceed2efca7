"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const pricingPlanSchema = new mongoose_1.Schema({
    type: {
        type: String,
        enum: ['temporary_key', 'spare_key', 'all_access_key'],
        required: true,
        unique: true
    },
    price: {
        type: Number,
        required: true,
        min: 0
    },
    displayPrice: {
        type: String,
        required: true
    },
    tagline: {
        type: String,
        required: true
    },
    features: {
        type: [String],
        required: true,
        validate: {
            validator: function (features) {
                return features.length > 0;
            },
            message: 'At least one feature is required'
        }
    },
    duration: {
        type: Number,
        required: true,
        default: function () {
            // Default to 1 month for all plans except temporary_key which gets infinite (-1)
            return this.type === 'temporary_key' ? -1 : 1;
        },
        validate: {
            validator: function (duration) {
                // Duration must be -1 (infinite) or positive number
                return duration === -1 || duration > 0;
            },
            message: 'Duration must be -1 (infinite) or a positive number of months'
        }
    },
    categorylimit: {
        type: Number,
        required: true,
        default: function () {
            // Default category limits based on plan type
            if (this.type === 'temporary_key')
                return 1;
            return -1; // Unlimited for spare_key and all_access_key
        },
        validate: {
            validator: function (limit) {
                // Category limit must be -1 (unlimited) or positive number
                return limit === -1 || limit > 0;
            },
            message: 'Category limit must be -1 (unlimited) or a positive number'
        }
    },
    active: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Add index for efficient querying
pricingPlanSchema.index({ type: 1 });
pricingPlanSchema.index({ active: 1 });
const PricingPlan = mongoose_1.default.model('PricingPlan', pricingPlanSchema);
exports.default = PricingPlan;
//# sourceMappingURL=PricingPlan.js.map