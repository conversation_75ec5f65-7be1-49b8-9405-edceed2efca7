"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Question = void 0;
const mongoose_1 = __importStar(require("mongoose"));
// Answer schema
const AnswerSchema = new mongoose_1.Schema({
    index: { type: Number, required: true, default: 0 },
    questionId: { type: mongoose_1.Schema.Types.ObjectId, default: () => new mongoose_1.Types.ObjectId() },
    question: { type: String, required: true, default: '' },
    type: {
        type: String,
        enum: ['boolean', 'text', 'number', 'choice', 'date'],
        default: 'text',
        required: true
    }
});
// Section schema
const AnswerBySectionSchema = new mongoose_1.Schema({
    sectionId: { type: mongoose_1.Schema.Types.ObjectId, default: () => new mongoose_1.Types.ObjectId() },
    isCompleted: { type: Boolean, default: false },
    answers: { type: [AnswerSchema], default: [] }
});
// Main schema
const QuestionSchema = new mongoose_1.Schema({
    subCategoryId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'SubCategory' },
    categoryId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Category' },
    answersBySection: { type: [AnswerBySectionSchema], default: [] }
}, {
    timestamps: true
});
// Export model
exports.Question = mongoose_1.default.model('Question', QuestionSchema);
//# sourceMappingURL=Question.js.map