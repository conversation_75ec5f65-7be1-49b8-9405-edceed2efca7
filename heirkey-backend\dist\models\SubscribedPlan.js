"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const subscribedPlanSchema = new mongoose_1.Schema({
    planId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'PricingPlan',
        required: true
    },
    ownerId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Owner',
        required: true
    },
    previousPlans: {
        type: [mongoose_1.Schema.Types.ObjectId],
        ref: 'PricingPlan',
        default: []
    },
    currentPlan: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'PricingPlan',
        required: true
    },
    expiryAt: {
        type: Date,
        required: false // Will be set by pre-save middleware
    }
}, {
    timestamps: true
});
// Pre-save middleware to calculate expiryAt based on plan duration
subscribedPlanSchema.pre('save', function (next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Always calculate expiryAt if it's not set or if currentPlan changed
            if (!this.expiryAt || this.isModified('currentPlan') || this.isNew) {
                // Get the current plan details to calculate expiry
                const PricingPlan = mongoose_1.default.model('PricingPlan');
                const plan = yield PricingPlan.findById(this.currentPlan);
                if (plan) {
                    const now = new Date();
                    if (plan.duration === -1) {
                        // Infinite duration - set expiry to far future (100 years from now)
                        this.expiryAt = new Date(now.getFullYear() + 100, now.getMonth(), now.getDate());
                    }
                    else {
                        // Calculate expiry based on plan duration in months
                        const expiryDate = new Date(now);
                        expiryDate.setMonth(expiryDate.getMonth() + (plan.duration || 1));
                        this.expiryAt = expiryDate;
                    }
                }
                else {
                    // Fallback: set expiry to 1 month from now if plan not found
                    const fallbackExpiry = new Date();
                    fallbackExpiry.setMonth(fallbackExpiry.getMonth() + 1);
                    this.expiryAt = fallbackExpiry;
                }
            }
        }
        catch (error) {
            console.error('Error calculating expiry date:', error);
            // Fallback: set expiry to 1 month from now
            const fallbackExpiry = new Date();
            fallbackExpiry.setMonth(fallbackExpiry.getMonth() + 1);
            this.expiryAt = fallbackExpiry;
        }
        next();
    });
});
// Add indexes for efficient querying
subscribedPlanSchema.index({ ownerId: 1 });
subscribedPlanSchema.index({ currentPlan: 1 });
subscribedPlanSchema.index({ expiryAt: 1 });
subscribedPlanSchema.index({ ownerId: 1, currentPlan: 1 }, { unique: true }); // One active subscription per owner
// Virtual to check if subscription is active
subscribedPlanSchema.virtual('isActive').get(function () {
    return new Date() < this.expiryAt;
});
// Virtual to get days remaining
subscribedPlanSchema.virtual('daysRemaining').get(function () {
    const now = new Date();
    const diffTime = this.expiryAt.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
});
// Ensure virtuals are included in JSON output
subscribedPlanSchema.set('toJSON', { virtuals: true });
subscribedPlanSchema.set('toObject', { virtuals: true });
const SubscribedPlan = mongoose_1.default.model('SubscribedPlan', subscribedPlanSchema);
exports.default = SubscribedPlan;
//# sourceMappingURL=SubscribedPlan.js.map