"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const categorySchema = new mongoose_1.default.Schema({
    name: { type: String, required: true },
    image: { type: String },
    numericId: { type: Number, unique: true, sparse: true }
}, { timestamps: true });
// Add text index for name field for text search capabilities
categorySchema.index({ name: 'text' });
const Category = mongoose_1.default.model('Category', categorySchema);
exports.default = Category;
//# sourceMappingURL=category.js.map