"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const encryption_1 = require("../utils/encryption");
// Schema for a single answer
const AnswerSchema = new mongoose_1.Schema({
    index: { type: Number, default: 0, required: true },
    questionId: { type: mongoose_1.Schema.Types.ObjectId },
    originalQuestionId: { type: String }, // Store our original question ID (q1, q2, etc.)
    question: { type: String },
    type: {
        type: String,
        enum: ['text', 'number', 'boolean', 'choice', 'date'],
        required: true,
        default: 'text',
    },
    answer: { type: String, required: true, default: '' },
    is_encrypted: { type: Boolean, default: false },
});
// Schema for user input by section
const UserInputBySectionSchema = new mongoose_1.Schema({
    sectionId: { type: mongoose_1.Schema.Types.ObjectId },
    originalSectionId: { type: String }, // Store our original section ID (101A, 101B, etc.)
    isCompleted: { type: Boolean },
    answers: { type: [AnswerSchema], default: [] }
});
// Main schema for user input
const UserInputSchema = new mongoose_1.Schema({
    userId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    ownerId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Owner', required: false }, // Made optional for backward compatibility
    categoryId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Category', required: true },
    originalCategoryId: { type: String }, // Store our original category ID (1, 2, etc.)
    subCategoryId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'SubCategory', required: true },
    originalSubCategoryId: { type: String }, // Store our original subcategory ID (101, 102, etc.)
    answersBySection: { type: [UserInputBySectionSchema], default: [] }
}, { timestamps: true });
// Pre-save middleware to encrypt answers
UserInputSchema.pre('save', function (next) {
    try {
        if (this.isModified('answersBySection') || this.isNew) {
            console.log('🔒 Encrypting answers before save...');
            const encryptedData = (0, encryption_1.encryptUserInputAnswers)(this.toObject());
            this.answersBySection = encryptedData.answersBySection;
        }
        next();
    }
    catch (error) {
        console.error('❌ Error encrypting answers:', error);
        next(error);
    }
});
// Post-find middleware to decrypt answers when retrieving
UserInputSchema.post(['find', 'findOne', 'findOneAndUpdate'], function (docs) {
    try {
        if (!docs)
            return;
        const docsArray = Array.isArray(docs) ? docs : [docs];
        docsArray.forEach((doc) => {
            if (doc && doc.answersBySection) {
                console.log('🔓 Decrypting answers after retrieval...');
                const decryptedData = (0, encryption_1.decryptUserInputAnswers)(doc.toObject ? doc.toObject() : doc);
                doc.answersBySection = decryptedData.answersBySection;
            }
        });
    }
    catch (error) {
        console.error('❌ Error decrypting answers:', error);
        // Don't throw error here to avoid breaking queries
    }
});
// Export the model
const UserInputModel = mongoose_1.default.model('UserInput', UserInputSchema);
exports.default = UserInputModel;
//# sourceMappingURL=userInput.js.map