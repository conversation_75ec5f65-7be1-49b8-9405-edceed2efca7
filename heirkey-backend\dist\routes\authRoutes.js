"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authController_1 = require("../controller/authController");
const userValidation_1 = require("../validation/userValidation");
const authMiddleware_1 = require("../middleware/authMiddleware");
const multerMiddleware_1 = __importDefault(require("../middleware/multerMiddleware"));
const router = express_1.default.Router();
// Public routes
router.post('/register', userValidation_1.registerValidation, authController_1.registerUser);
router.post('/login', userValidation_1.loginValidation, authController_1.loginUser);
router.post('/verify-email', authController_1.verifyEmail);
router.post('/resend-verification', authController_1.resendVerificationOTP);
router.post('/forget-password', authController_1.forgetPassword);
router.post('/reset-password/:token', authController_1.resetPassword);
// Debug routes (development only)
router.get('/debug/verification-status/:email', authController_1.getVerificationStatus);
// Test email endpoint (remove in production)
//
// Protected routes - using combined authentication
router.get('/profile', authMiddleware_1.combinedAuth, authController_1.getProfile);
router.put('/profile', authMiddleware_1.combinedAuth, userValidation_1.profileUpdateValidation, authController_1.updateUserProfile);
router.post('/profile/image', authMiddleware_1.combinedAuth, multerMiddleware_1.default.single('image'), authController_1.updateUserProfileImage);
router.post('/logout', authMiddleware_1.combinedAuth, authController_1.logout);
// Admin routes
router.get('/users', authMiddleware_1.combinedAuth, authController_1.getAllUsers);
router.post('/users', authMiddleware_1.combinedAuth, multerMiddleware_1.default.single('image'), authController_1.userDetails);
exports.default = router;
//# sourceMappingURL=authRoutes.js.map