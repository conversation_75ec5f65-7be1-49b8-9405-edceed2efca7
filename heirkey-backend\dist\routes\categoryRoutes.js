"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const categoryController_1 = require("../controller/categoryController");
const router = express_1.default.Router();
router.post('/', categoryController_1.category);
router.get('/all', categoryController_1.getAllCategories);
// Utility route to ensure default categories exist
// router.get('/ensure-defaults', ensureDefaultCategories);
// Optional routes
router.get('/', categoryController_1.getCategories);
router.get('/subcategories', categoryController_1.getFullCategoryDataQuestions);
exports.default = router;
//# sourceMappingURL=categoryRoutes.js.map