"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const googleAuth_1 = require("../controller/googleAuth");
const passport_1 = __importDefault(require("passport"));
const router = express_1.default.Router();
// Route to initiate Google login
router.get('/v1/auth/google/login', (req, res, next) => {
    passport_1.default.authenticate('google', {
        scope: ['profile', 'email'],
        state: 'login',
        session: true
    })(req, res, next);
});
// Route to initiate Google signup
router.get('/v1/auth/google/signup', (req, res, next) => {
    passport_1.default.authenticate('google', {
        scope: ['profile', 'email'],
        state: 'signup',
        session: true
    })(req, res, next);
});
// Route to handle the callback from Google
router.get('/v1/auth/google/callback', passport_1.default.authenticate('google', {
    failureRedirect: '/v1/auth/google/failure',
    failureMessage: true,
    session: true
}), googleAuth_1.googleAuthCallback);
// Handle authentication failure
router.get('/v1/auth/google/failure', (req, res) => {
    var _a, _b;
    const state = req.query.state || 'login';
    const message = ((_b = (_a = req.session) === null || _a === void 0 ? void 0 : _a.messages) === null || _b === void 0 ? void 0 : _b[req.session.messages.length - 1]) || 'Authentication failed';
    if (state === 'login') {
        return res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(message)}&redirectTo=register`);
    }
    res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(message)}`);
});
router.get('/v1/profile', googleAuth_1.profileDetails);
exports.default = router;
//# sourceMappingURL=googleAuthRoutes.js.map