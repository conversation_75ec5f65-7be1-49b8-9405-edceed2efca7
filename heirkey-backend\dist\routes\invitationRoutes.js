"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const invitationController_1 = require("../controller/invitationController");
const invitationValidation_1 = require("../validation/invitationValidation");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// Test email endpoint
// router.post('/test-email', async (req, res) => {
//     try {
//         const { to } = req.body;
//         await sendEmail({
//             to: to || '<EMAIL>',
//             subject: 'Test Email from Heirkey',
//             html: '<h1>Test Email</h1><p>If you receive this, email configuration is working!</p>',
//             text: 'Test Email - If you receive this, email configuration is working!'
//         });
//         res.status(200).json({ message: 'Test email sent successfully!' });
//     } catch (error) {
//         console.error('Test email error:', error);
//         res.status(500).json({
//             message: 'Failed to send test email',
//             error: error instanceof Error ? error.message : 'Unknown error'
//         });
//     }
// });
// Protected routes - Owner can invite users
router.post('/invite', authMiddleware_1.combinedAuth, invitationValidation_1.inviteUserValidation, invitationController_1.inviteUser);
router.get('/sent', authMiddleware_1.combinedAuth, invitationController_1.getInvitations);
// Public route - Accept invitation (no auth required as user doesn't have account yet)
router.post('/accept', invitationValidation_1.acceptInvitationValidation, invitationController_1.acceptInvitation);
exports.default = router;
//# sourceMappingURL=invitationRoutes.js.map