"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const ownerController_1 = require("../controller/ownerController");
const ownerValidation_1 = require("../validation/ownerValidation");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// Apply authentication to all routes
router.use(authMiddleware_1.combinedAuth);
// Owner management routes
router.get('/', ownerController_1.getAllOwners); // GET /owners - Get all owners
router.get('/profile', ownerController_1.getMyOwnerProfile); // GET /owners/profile - Get current user's owner profile
router.put('/profile', ownerValidation_1.validateUpdateOwner, ownerController_1.updateMyOwnerProfile); // PUT /owners/profile - Update current user's owner profile
router.get('/user/:userId', ownerController_1.getOwnerByUserId); // GET /owners/user/:userId - Get owner by user ID
router.get('/email/:email', ownerController_1.getOwnerByEmail); // GET /owners/email/:email - Get owner by email
router.get('/:id', ownerController_1.getOwnerById); // GET /owners/:id - Get owner by ID
router.put('/:id', ownerValidation_1.validateUpdateOwner, ownerController_1.updateOwner); // PUT /owners/:id - Update owner by ID
router.delete('/:id', ownerController_1.deleteOwner); // DELETE /owners/:id - Delete owner by ID
exports.default = router;
//# sourceMappingURL=ownerRoutes.js.map