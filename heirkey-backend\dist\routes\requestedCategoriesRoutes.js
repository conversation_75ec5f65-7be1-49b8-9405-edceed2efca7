"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const requestedCategoriesController_1 = require("../controller/requestedCategoriesController");
const requestedCategoriesValidation_1 = require("../validation/requestedCategoriesValidation");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// Protected routes - Nominee/Family can request categories
router.post('/request', authMiddleware_1.combinedAuth, requestedCategoriesValidation_1.requestCategoriesValidation, requestedCategoriesController_1.requestCategories);
router.get('/my-requests', authMiddleware_1.combinedAuth, requestedCategoriesController_1.getMyRequests);
// Protected routes - Owner can view requests
router.get('/owner-requests', authMiddleware_1.combinedAuth, requestedCategoriesController_1.getOwnerRequests);
// Public route - Approve/reject request (no auth required as it uses token)
router.post('/approve', requestedCategoriesValidation_1.approveRequestValidation, requestedCategoriesController_1.approveRequest);
exports.default = router;
//# sourceMappingURL=requestedCategoriesRoutes.js.map