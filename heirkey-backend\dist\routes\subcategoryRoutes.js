"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const subcategoryController_1 = require("../controller/subcategoryController");
const router = express_1.default.Router();
router.post('/', subcategoryController_1.subcategory);
router.get('/', subcategoryController_1.SubcategoriesByCategoryId);
router.get('/questions', subcategoryController_1.getSubcategoryDetailsQuestions);
3;
exports.default = router;
//# sourceMappingURL=subcategoryRoutes.js.map