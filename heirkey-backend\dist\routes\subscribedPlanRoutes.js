"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const subscribedPlanController_1 = require("../controller/subscribedPlanController");
const subscribedPlanValidation_1 = require("../validation/subscribedPlanValidation");
const router = express_1.default.Router();
// Get all subscriptions (with optional filters)
router.get('/', subscribedPlanController_1.getAllSubscriptions);
// Get expired subscriptions
router.get('/expired', subscribedPlanController_1.getExpiredSubscriptions);
// Get subscription by ID
router.get('/:id', subscribedPlanController_1.getSubscriptionById);
// Get owner's current subscription
router.get('/owner/:ownerId', subscribedPlanController_1.getOwnerSubscription);
// Create new subscription
router.post('/', subscribedPlanValidation_1.validateSubscribedPlan, subscribedPlanController_1.createSubscription);
// Change/upgrade plan for an owner
router.put('/owner/:ownerId/change-plan', subscribedPlanValidation_1.validateChangePlan, subscribedPlanController_1.changePlan);
// Renew subscription (extend current plan)
router.put('/:id/renew', subscribedPlanController_1.renewSubscription);
// Cancel subscription
router.delete('/:id', subscribedPlanController_1.cancelSubscription);
exports.default = router;
//# sourceMappingURL=subscribedPlanRoutes.js.map