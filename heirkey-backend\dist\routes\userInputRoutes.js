"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const userInputController_1 = require("../controller/userInputController");
const router = express_1.default.Router();
// Define the dashboard stats route (now owner-based only)
router.get('/dashboard/stats', userInputController_1.getDashboardStats);
// Define the owner-based routes
router.get('/by-owner', userInputController_1.getUserInputByOwnerId);
// Then define the other routes
router.post('/', userInputController_1.createUserInput);
router.get('/:id', userInputController_1.getUserInput);
router.get('/', userInputController_1.getUserInputByUserId);
router.patch('/:id', userInputController_1.updateUserInput);
exports.default = router;
//# sourceMappingURL=userInputRoutes.js.map