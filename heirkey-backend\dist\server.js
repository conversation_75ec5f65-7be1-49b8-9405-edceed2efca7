"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = __importDefault(require("./config/db"));
const app_1 = __importDefault(require("./app"));
dotenv_1.default.config();
const PORT = process.env.PORT || 3000;
// const PUBLIC_IP = '*************';
const PUBLIC_IP = '*************';
(0, db_1.default)();
app_1.default.listen(Number(PORT), '0.0.0.0', () => {
    console.log(`Server is running on http://localhost:${PORT}`);
    console.log(`Server is running on http://0.0.0.0:${PORT}`);
    console.log(`You can access it at http://${PUBLIC_IP}:${PORT}`);
});
//# sourceMappingURL=server.js.map