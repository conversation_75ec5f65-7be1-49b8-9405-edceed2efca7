"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generatePasswordResetSuccessTemplate = exports.generatePasswordResetTemplate = exports.generateEmailVerificationTemplate = void 0;
const generateEmailVerificationTemplate = (username, otp) => {
    const textContent = `
Hello ${username},

Welcome to <PERSON><PERSON><PERSON>! Please verify your email address to complete your registration.

Your verification code is: ${otp}

This code is valid for 10 minutes only.

If you did not create an account with <PERSON><PERSON><PERSON>, please ignore this email.

Best regards,
The Heirkey Team
  `.trim();
    const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #1F2668; margin: 0;">Welcome to <PERSON><PERSON><PERSON>!</h1>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #1F2668; margin-top: 0;">Email Verification Required</h2>
        <p>Hello ${username},</p>
        <p>Thank you for signing up with Heirkey! To complete your registration and secure your account, please verify your email address.</p>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <div style="background-color: #2BCFD5; color: white; padding: 20px; border-radius: 8px; display: inline-block;">
          <h3 style="margin: 0; font-size: 24px; letter-spacing: 3px;">${otp}</h3>
          <p style="margin: 5px 0 0 0; font-size: 14px;">Verification Code</p>
        </div>
      </div>

      <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="margin: 0; color: #856404;">
          <strong>⏰ Important:</strong> This verification code is valid for <strong>10 minutes only</strong>.
        </p>
      </div>

      <p>Enter this code in the verification form to activate your account and start using Heirkey.</p>
      
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        If you did not create an account with Heirkey, please ignore this email. Your email address will not be added to our system.
      </p>

      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      
      <div style="text-align: center; color: #666; font-size: 12px;">
        <p>Best regards,<br>The Heirkey Team</p>
        <p>This is an automated message, please do not reply to this email.</p>
      </div>
    </div>
  `;
    return {
        subject: 'Verify Your Email - Heirkey Account Activation',
        text: textContent,
        html: htmlContent
    };
};
exports.generateEmailVerificationTemplate = generateEmailVerificationTemplate;
const generatePasswordResetTemplate = (username, resetUrl) => {
    const textContent = `
Hello ${username},

We have received a password reset request for your Heirkey account. Please use the link below to reset your password:

${resetUrl}

This link is valid for 10 minutes.

If you did not request this password reset, please ignore this email.

Best regards,
The Heirkey Team
  `.trim();
    const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h2 style="color: #1F2668;">Password Reset Request</h2>
      <p>Hello ${username},</p>
      <p>We have received a password reset request for your Heirkey account.</p>
      <p>Please click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" style="background-color: #2BCFD5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
      </div>
      <p>Or copy and paste this link into your browser:</p>
      <p style="word-break: break-all; color: #666;">${resetUrl}</p>
      <p><strong>This link is valid for 10 minutes only.</strong></p>
      <p>If you did not request this password reset, please ignore this email.</p>
      <p>Best regards,<br>The Heirkey Team</p>
    </div>
  `;
    return {
        subject: 'Password Reset Request - Heirkey',
        text: textContent,
        html: htmlContent
    };
};
exports.generatePasswordResetTemplate = generatePasswordResetTemplate;
const generatePasswordResetSuccessTemplate = (username) => {
    const textContent = `
Hello ${username},

Your password has been successfully reset for your Heirkey account.

If you did not make this change, please contact our support team immediately.

Best regards,
The Heirkey Team
  `.trim();
    const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1F2668;">Password Reset Successful</h2>
      <p>Hello ${username},</p>
      <p>Your password has been successfully reset for your Heirkey account.</p>
      <p>If you did not make this change, please contact our support team immediately.</p>
      <p>Best regards,<br>The Heirkey Team</p>
    </div>
  `;
    return {
        subject: 'Password Reset Successful - Heirkey',
        text: textContent,
        html: htmlContent
    };
};
exports.generatePasswordResetSuccessTemplate = generatePasswordResetSuccessTemplate;
//# sourceMappingURL=emailTemplates.js.map