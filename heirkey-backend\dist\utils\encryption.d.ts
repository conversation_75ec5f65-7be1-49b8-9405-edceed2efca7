/**
 * Encrypt a string using AES-256-GCM
 * @param text - The text to encrypt
 * @returns Encrypted string in format: salt + iv + tag + encrypted_data (all base64 encoded)
 */
export declare const encrypt: (text: string) => string;
/**
 * Decrypt a string using AES-256-GCM
 * @param encryptedData - The encrypted data to decrypt
 * @returns Decrypted string
 */
export declare const decrypt: (encryptedData: string) => string;
/**
 * Encrypt an answer object
 * @param answer - The answer object to encrypt
 * @returns Answer object with encrypted answer field
 */
export declare const encryptAnswer: (answer: any) => any;
/**
 * Decrypt an answer object
 * @param answer - The answer object to decrypt
 * @returns Answer object with decrypted answer field
 */
export declare const decryptAnswer: (answer: any) => any;
/**
 * Encrypt all answers in a user input object
 * @param userInput - The user input object
 * @returns User input object with encrypted answers
 */
export declare const encryptUserInputAnswers: (userInput: any) => any;
/**
 * Decrypt all answers in a user input object
 * @param userInput - The user input object
 * @returns User input object with decrypted answers
 */
export declare const decryptUserInputAnswers: (userInput: any) => any;
