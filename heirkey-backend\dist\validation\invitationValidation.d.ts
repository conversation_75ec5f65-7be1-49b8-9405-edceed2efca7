import { Request, Response, NextFunction } from 'express';
export declare const inviteUserValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const acceptInvitationValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const updateInvitationStatusValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
