import { Request, Response, NextFunction } from 'express';
export declare const validateCreateOwner: (import("express-validator").ValidationChain | typeof handleValidationErrors)[];
export declare const validateUpdateOwner: (import("express-validator").ValidationChain | typeof handleValidationErrors)[];
export declare const validateGetOwnerById: (import("express-validator").ValidationChain | typeof handleValidationErrors)[];
export declare const validateGetOwnerByUserId: (import("express-validator").ValidationChain | typeof handleValidationErrors)[];
declare function handleValidationErrors(req: Request, res: Response, next: NextFunction): void;
export {};
