"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateGetOwnerByUserId = exports.validateGetOwnerById = exports.validateUpdateOwner = exports.validateCreateOwner = void 0;
const express_validator_1 = require("express-validator");
const express_validator_2 = require("express-validator");
// Validation for creating owner
exports.validateCreateOwner = [
    (0, express_validator_1.body)('userId')
        .notEmpty()
        .withMessage('User ID is required')
        .isMongoId()
        .withMessage('User ID must be a valid MongoDB ObjectId'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .withMessage('Please provide a valid email')
        .normalizeEmail()
        .toLowerCase(),
    (0, express_validator_1.body)('username')
        .optional()
        .isLength({ min: 3, max: 30 })
        .withMessage('Username must be between 3 and 30 characters')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Username can only contain letters, numbers, and underscores'),
    (0, express_validator_1.body)('firstName')
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name must be between 1 and 50 characters')
        .trim(),
    (0, express_validator_1.body)('lastName')
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name must be between 1 and 50 characters')
        .trim(),
    (0, express_validator_1.body)('googleId')
        .optional()
        .isString()
        .withMessage('Google ID must be a string'),
    (0, express_validator_1.body)('externalUser')
        .optional()
        .isBoolean()
        .withMessage('External user must be a boolean value'),
    handleValidationErrors
];
// Validation for updating owner
exports.validateUpdateOwner = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Owner ID must be a valid MongoDB ObjectId'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .withMessage('Please provide a valid email')
        .normalizeEmail()
        .toLowerCase(),
    (0, express_validator_1.body)('username')
        .optional()
        .isLength({ min: 3, max: 30 })
        .withMessage('Username must be between 3 and 30 characters')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Username can only contain letters, numbers, and underscores'),
    (0, express_validator_1.body)('firstName')
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name must be between 1 and 50 characters')
        .trim(),
    (0, express_validator_1.body)('lastName')
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name must be between 1 and 50 characters')
        .trim(),
    handleValidationErrors
];
// Validation for getting owner by ID
exports.validateGetOwnerById = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Owner ID must be a valid MongoDB ObjectId'),
    handleValidationErrors
];
// Validation for getting owner by user ID
exports.validateGetOwnerByUserId = [
    (0, express_validator_1.param)('userId')
        .isMongoId()
        .withMessage('User ID must be a valid MongoDB ObjectId'),
    handleValidationErrors
];
// Error handling middleware
function handleValidationErrors(req, res, next) {
    const errors = (0, express_validator_2.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            status: 'fail',
            message: 'Validation failed',
            errors: errors.array()
        });
        return;
    }
    next();
}
//# sourceMappingURL=ownerValidation.js.map