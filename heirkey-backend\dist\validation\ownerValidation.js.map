{"version": 3, "file": "ownerValidation.js", "sourceRoot": "", "sources": ["../../src/validation/ownerValidation.ts"], "names": [], "mappings": ";;;AAAA,yDAAgD;AAChD,yDAAqD;AAGrD,gCAAgC;AACnB,QAAA,mBAAmB,GAAG;IACjC,IAAA,wBAAI,EAAC,QAAQ,CAAC;SACX,QAAQ,EAAE;SACV,WAAW,CAAC,qBAAqB,CAAC;SAClC,SAAS,EAAE;SACX,WAAW,CAAC,0CAA0C,CAAC;IAE1D,IAAA,wBAAI,EAAC,OAAO,CAAC;SACV,OAAO,EAAE;SACT,WAAW,CAAC,8BAA8B,CAAC;SAC3C,cAAc,EAAE;SAChB,WAAW,EAAE;IAEhB,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,8CAA8C,CAAC;SAC3D,OAAO,CAAC,iBAAiB,CAAC;SAC1B,WAAW,CAAC,6DAA6D,CAAC;IAE7E,IAAA,wBAAI,EAAC,WAAW,CAAC;SACd,QAAQ,EAAE;SACV,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,gDAAgD,CAAC;SAC7D,IAAI,EAAE;IAET,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,+CAA+C,CAAC;SAC5D,IAAI,EAAE;IAET,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,EAAE;SACV,WAAW,CAAC,4BAA4B,CAAC;IAE5C,IAAA,wBAAI,EAAC,cAAc,CAAC;SACjB,QAAQ,EAAE;SACV,SAAS,EAAE;SACX,WAAW,CAAC,uCAAuC,CAAC;IAEvD,sBAAsB;CACvB,CAAC;AAEF,gCAAgC;AACnB,QAAA,mBAAmB,GAAG;IACjC,IAAA,yBAAK,EAAC,IAAI,CAAC;SACR,SAAS,EAAE;SACX,WAAW,CAAC,2CAA2C,CAAC;IAE3D,IAAA,wBAAI,EAAC,OAAO,CAAC;SACV,QAAQ,EAAE;SACV,OAAO,EAAE;SACT,WAAW,CAAC,8BAA8B,CAAC;SAC3C,cAAc,EAAE;SAChB,WAAW,EAAE;IAEhB,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,8CAA8C,CAAC;SAC3D,OAAO,CAAC,iBAAiB,CAAC;SAC1B,WAAW,CAAC,6DAA6D,CAAC;IAE7E,IAAA,wBAAI,EAAC,WAAW,CAAC;SACd,QAAQ,EAAE;SACV,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,gDAAgD,CAAC;SAC7D,IAAI,EAAE;IAET,IAAA,wBAAI,EAAC,UAAU,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC7B,WAAW,CAAC,+CAA+C,CAAC;SAC5D,IAAI,EAAE;IAET,sBAAsB;CACvB,CAAC;AAEF,qCAAqC;AACxB,QAAA,oBAAoB,GAAG;IAClC,IAAA,yBAAK,EAAC,IAAI,CAAC;SACR,SAAS,EAAE;SACX,WAAW,CAAC,2CAA2C,CAAC;IAE3D,sBAAsB;CACvB,CAAC;AAEF,0CAA0C;AAC7B,QAAA,wBAAwB,GAAG;IACtC,IAAA,yBAAK,EAAC,QAAQ,CAAC;SACZ,SAAS,EAAE;SACX,WAAW,CAAC,0CAA0C,CAAC;IAE1D,sBAAsB;CACvB,CAAC;AAEF,4BAA4B;AAC5B,SAAS,sBAAsB,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAC7E,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAC;IAErC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QACtB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,mBAAmB;YAC5B,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE;SACvB,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC"}