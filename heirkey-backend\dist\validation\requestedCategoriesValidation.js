"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateRequestStatusValidation = exports.approveRequestValidation = exports.requestCategoriesValidation = void 0;
const joi_1 = __importDefault(require("joi"));
const requestCategoriesSchema = joi_1.default.object({
    categoryIds: joi_1.default.array()
        .items(joi_1.default.string().pattern(/^[0-9]+$/))
        .min(1)
        .max(10)
        .required()
        .messages({
        'array.min': 'At least one category must be requested',
        'array.max': 'Cannot request more than 10 categories at once',
        'string.pattern.base': 'Category ID must be a numeric string (e.g., "1", "2", "3")'
    }),
    message: joi_1.default.string()
        .optional()
        .max(500)
        .trim()
        .messages({
        'string.max': 'Message cannot exceed 500 characters'
    })
});
const approveRequestSchema = joi_1.default.object({
    token: joi_1.default.string().required(),
    action: joi_1.default.string().valid('approve', 'reject').required()
});
const updateRequestStatusSchema = joi_1.default.object({
    status: joi_1.default.string().valid('pending', 'approved', 'rejected').required()
});
const requestCategoriesValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield requestCategoriesSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.requestCategoriesValidation = requestCategoriesValidation;
const approveRequestValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield approveRequestSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.approveRequestValidation = approveRequestValidation;
const updateRequestStatusValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield updateRequestStatusSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.updateRequestStatusValidation = updateRequestStatusValidation;
//# sourceMappingURL=requestedCategoriesValidation.js.map