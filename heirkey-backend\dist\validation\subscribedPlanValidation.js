"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateChangePlan = exports.validateSubscribedPlanUpdate = exports.validateSubscribedPlan = void 0;
const joi_1 = __importDefault(require("joi"));
const subscribedPlanSchema = joi_1.default.object({
    planId: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .optional()
        .messages({
        'string.pattern.base': 'Plan ID must be a valid MongoDB ObjectId'
    }),
    ownerId: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .required()
        .messages({
        'string.pattern.base': 'Owner ID must be a valid MongoDB ObjectId',
        'any.required': 'Owner ID is required'
    }),
    previousPlans: joi_1.default.array()
        .items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/))
        .optional()
        .default([])
        .messages({
        'string.pattern.base': 'Previous plan IDs must be valid MongoDB ObjectIds'
    }),
    currentPlan: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .optional()
        .messages({
        'string.pattern.base': 'Current plan ID must be a valid MongoDB ObjectId'
    })
}).or('planId', 'currentPlan').messages({
    'object.missing': 'Either planId or currentPlan is required'
});
const updateSubscribedPlanSchema = joi_1.default.object({
    planId: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .optional()
        .messages({
        'string.pattern.base': 'Plan ID must be a valid MongoDB ObjectId'
    }),
    ownerId: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .optional()
        .messages({
        'string.pattern.base': 'Owner ID must be a valid MongoDB ObjectId'
    }),
    previousPlans: joi_1.default.array()
        .items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/))
        .optional()
        .messages({
        'string.pattern.base': 'Previous plan IDs must be valid MongoDB ObjectIds'
    }),
    currentPlan: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .optional()
        .messages({
        'string.pattern.base': 'Current plan ID must be a valid MongoDB ObjectId'
    })
});
const changePlanSchema = joi_1.default.object({
    newPlanId: joi_1.default.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .required()
        .messages({
        'string.pattern.base': 'New plan ID must be a valid MongoDB ObjectId',
        'any.required': 'New plan ID is required'
    })
});
const validateSubscribedPlan = (req, res, next) => {
    const { error } = subscribedPlanSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            message: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validateSubscribedPlan = validateSubscribedPlan;
const validateSubscribedPlanUpdate = (req, res, next) => {
    const { error } = updateSubscribedPlanSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            message: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validateSubscribedPlanUpdate = validateSubscribedPlanUpdate;
const validateChangePlan = (req, res, next) => {
    const { error } = changePlanSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            message: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validateChangePlan = validateChangePlan;
//# sourceMappingURL=subscribedPlanValidation.js.map