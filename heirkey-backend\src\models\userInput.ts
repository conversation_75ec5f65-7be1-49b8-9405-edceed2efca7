import mongoose, { Schema, Document, Types } from 'mongoose';
import { IAnswer, IUserInput } from '../types/UserInput';
import { encryptUserInputAnswers, decryptUserInputAnswers } from '../utils/encryption';

// Schema for a single answer
const AnswerSchema = new Schema<IAnswer>({
  index: { type: Number, default: 0, required: true },
  questionId: { type: Schema.Types.ObjectId },
  originalQuestionId: { type: String }, // Store our original question ID (q1, q2, etc.)
  question: { type: String },
  type: {
    type: String,
    enum: ['text', 'number', 'boolean', 'choice', 'date'],
    required: true,
    default: 'text',
  },
  answer: { type: String, required: true, default: '' },
  is_encrypted: { type: Boolean, default: false },
});

// Schema for user input by section
const UserInputBySectionSchema = new Schema<IUserInput>({
  sectionId: { type: Schema.Types.ObjectId },
  originalSectionId: { type: String }, // Store our original section ID (101A, 101B, etc.)
  isCompleted: { type: Boolean },
  answers: { type: [AnswerSchema], default: [] }
});

// Main schema for user input
const UserInputSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  ownerId: { type: Schema.Types.ObjectId, ref: 'Owner', required: false }, // Made optional for backward compatibility
  categoryId: { type: Schema.Types.ObjectId, ref: 'Category', required: true },
  originalCategoryId: { type: String }, // Store our original category ID (1, 2, etc.)
  subCategoryId: { type: Schema.Types.ObjectId, ref: 'SubCategory', required: true },
  originalSubCategoryId: { type: String }, // Store our original subcategory ID (101, 102, etc.)
  answersBySection: { type: [UserInputBySectionSchema], default: [] }
}, { timestamps: true });

// Pre-save middleware to encrypt answers
UserInputSchema.pre('save', function(next) {
  try {
    if (this.isModified('answersBySection') || this.isNew) {
      console.log('🔒 Encrypting answers before save...');
      const encryptedData = encryptUserInputAnswers(this.toObject());
      this.answersBySection = encryptedData.answersBySection;
    }
    next();
  } catch (error) {
    console.error('❌ Error encrypting answers:', error);
    next(error as mongoose.CallbackError);
  }
});

// Post-find middleware to decrypt answers when retrieving
UserInputSchema.post(['find', 'findOne', 'findOneAndUpdate'], function(docs) {
  try {
    if (!docs) return;

    const docsArray = Array.isArray(docs) ? docs : [docs];

    docsArray.forEach((doc) => {
      if (doc && doc.answersBySection) {
        console.log('🔓 Decrypting answers after retrieval...');
        const decryptedData = decryptUserInputAnswers(doc.toObject ? doc.toObject() : doc);
        doc.answersBySection = decryptedData.answersBySection;
      }
    });
  } catch (error) {
    console.error('❌ Error decrypting answers:', error);
    // Don't throw error here to avoid breaking queries
  }
});

// Export the model
const UserInputModel = mongoose.model<IUserInput>('UserInput', UserInputSchema);
export default UserInputModel;
