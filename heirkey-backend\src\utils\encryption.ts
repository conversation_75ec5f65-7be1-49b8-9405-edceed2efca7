import crypto from 'crypto';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For GCM, this is always 16
const SALT_LENGTH = 64;
const TAG_LENGTH = 16;
const TAG_POSITION = SALT_LENGTH + IV_LENGTH;
const ENCRYPTED_POSITION = TAG_POSITION + TAG_LENGTH;

/**
 * Get encryption key from environment variable
 * If not set, generate a warning and use a default (not recommended for production)
 */
const getEncryptionKey = (): string => {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) {
    console.warn('⚠️  ENCRYPTION_KEY not set in environment variables. Using default key (NOT SECURE FOR PRODUCTION)');
    return 'default-key-not-secure-change-this-in-production-environment';
  }
  return key;
};

/**
 * Derive key from password using PBKDF2
 */
const deriveKey = (password: string, salt: Buffer): Buffer => {
  return crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');
};

/**
 * Encrypt a string using AES-256-GCM
 * @param text - The text to encrypt
 * @returns Encrypted string in format: salt + iv + tag + encrypted_data (all base64 encoded)
 */
export const encrypt = (text: string): string => {
  try {
    if (!text || typeof text !== 'string') {
      return text; // Return as-is if not a valid string
    }

    const password = getEncryptionKey();
    const salt = crypto.randomBytes(SALT_LENGTH);
    const iv = crypto.randomBytes(IV_LENGTH);
    const key = deriveKey(password, salt);

    const cipher = crypto.createCipheriv(ALGORITHM, key, iv) as crypto.CipherGCM;
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();

    // Combine salt + iv + tag + encrypted data
    const combined = Buffer.concat([
      salt,
      iv,
      tag,
      Buffer.from(encrypted, 'hex')
    ]);

    return combined.toString('base64');
  } catch (error) {
    console.error('❌ Encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypt a string using AES-256-GCM
 * @param encryptedData - The encrypted data to decrypt
 * @returns Decrypted string
 */
export const decrypt = (encryptedData: string): string => {
  try {
    if (!encryptedData || typeof encryptedData !== 'string') {
      return encryptedData; // Return as-is if not a valid string
    }

    // Check if the data looks like it's encrypted (base64 format)
    if (!isBase64(encryptedData)) {
      return encryptedData; // Return as-is if not base64 (probably not encrypted)
    }

    const password = getEncryptionKey();
    const combined = Buffer.from(encryptedData, 'base64');

    // Extract components
    const salt = combined.subarray(0, SALT_LENGTH);
    const iv = combined.subarray(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
    const tag = combined.subarray(TAG_POSITION, ENCRYPTED_POSITION);
    const encrypted = combined.subarray(ENCRYPTED_POSITION);

    const key = deriveKey(password, salt);

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv) as crypto.DecipherGCM;
    decipher.setAuthTag(tag);

    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('❌ Decryption failed:', error);
    // Return original data if decryption fails (might be unencrypted legacy data)
    return encryptedData;
  }
};

/**
 * Check if a string is valid base64
 */
const isBase64 = (str: string): boolean => {
  try {
    return Buffer.from(str, 'base64').toString('base64') === str;
  } catch (err) {
    return false;
  }
};

/**
 * Encrypt an answer object
 * @param answer - The answer object to encrypt
 * @returns Answer object with encrypted answer field
 */
export const encryptAnswer = (answer: any): any => {
  if (!answer || !answer.answer) {
    return answer;
  }

  return {
    ...answer,
    answer: encrypt(answer.answer),
    is_encrypted: true
  };
};

/**
 * Decrypt an answer object
 * @param answer - The answer object to decrypt
 * @returns Answer object with decrypted answer field
 */
export const decryptAnswer = (answer: any): any => {
  if (!answer || !answer.answer || !answer.is_encrypted) {
    return answer;
  }

  return {
    ...answer,
    answer: decrypt(answer.answer),
    is_encrypted: true // Keep the flag to know it was encrypted
  };
};

/**
 * Encrypt all answers in a user input object
 * @param userInput - The user input object
 * @returns User input object with encrypted answers
 */
export const encryptUserInputAnswers = (userInput: any): any => {
  if (!userInput || !userInput.answersBySection) {
    return userInput;
  }

  const encryptedAnswersBySection = userInput.answersBySection.map((section: any) => ({
    ...section,
    answers: section.answers ? section.answers.map(encryptAnswer) : []
  }));

  return {
    ...userInput,
    answersBySection: encryptedAnswersBySection
  };
};

/**
 * Decrypt all answers in a user input object
 * @param userInput - The user input object
 * @returns User input object with decrypted answers
 */
export const decryptUserInputAnswers = (userInput: any): any => {
  if (!userInput || !userInput.answersBySection) {
    return userInput;
  }

  const decryptedAnswersBySection = userInput.answersBySection.map((section: any) => ({
    ...section,
    answers: section.answers ? section.answers.map(decryptAnswer) : []
  }));

  return {
    ...userInput,
    answersBySection: decryptedAnswersBySection
  };
};
