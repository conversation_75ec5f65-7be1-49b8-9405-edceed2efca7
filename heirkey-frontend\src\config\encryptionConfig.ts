/**
 * Configuration for encryption settings in Heirkey frontend
 * Defines which questions and data types should be encrypted
 */

/**
 * Question patterns that should always be encrypted
 * These patterns are checked against question text and IDs
 */
export const SENSITIVE_QUESTION_PATTERNS = [
  // Authentication and security
  /password/i,
  /pin/i,
  /passcode/i,
  /security.code/i,
  /verification.code/i,
  
  // Personal identification
  /ssn/i,
  /social.security/i,
  /tax.id/i,
  /driver.license/i,
  /passport/i,
  
  // Financial information
  /account.number/i,
  /routing.number/i,
  /credit.card/i,
  /debit.card/i,
  /bank.account/i,
  /iban/i,
  /swift/i,
  
  // Medical information
  /medical.record/i,
  /health.insurance/i,
  /prescription/i,
  /diagnosis/i,
  
  // Legal and sensitive documents
  /will/i,
  /testament/i,
  /power.of.attorney/i,
  /legal.document/i,
  
  // Personal sensitive information
  /private.key/i,
  /secret/i,
  /confidential/i,
  /sensitive/i,
];

/**
 * Specific question IDs that should always be encrypted
 * Add question IDs that contain sensitive information
 */
export const SENSITIVE_QUESTION_IDS = [
  // Social media passwords
  's3', 's6', 's9', 's12', 's15', // Social media password fields
  
  // Banking and financial
  'bank_account', 'routing_number', 'account_number',
  'credit_card', 'debit_card', 'pin',
  
  // Personal identification
  'ssn', 'tax_id', 'driver_license', 'passport_number',
  
  // Medical
  'medical_record', 'health_insurance', 'prescription',
  
  // Legal documents
  'will_location', 'testament_details', 'power_of_attorney',
  
  // Security codes and passwords
  'password', 'passcode', 'security_code', 'verification_code',
  
  // Add more specific question IDs as needed
];

/**
 * Categories that should have all text answers encrypted
 * Use original category IDs (1, 2, 3, etc.)
 */
export const FULLY_ENCRYPTED_CATEGORIES = [
  '6', // Social Media - contains passwords and sensitive login info
  // Add more categories as needed
];

/**
 * Question types that should be encrypted by default
 */
export const SENSITIVE_QUESTION_TYPES = [
  'password',
  'secret',
  'confidential',
  // Add more types as needed
];

/**
 * Minimum answer length to consider for encryption
 * Very short answers (like "Yes/No") might not need encryption
 */
export const MIN_ENCRYPTION_LENGTH = 3;

/**
 * Check if a question should be encrypted based on configuration
 */
export const shouldEncryptQuestion = (
  question: {
    originalQuestionId: string;
    question?: string;
    type: string;
    answer: string;
  },
  categoryId?: string
): boolean => {
  // Don't encrypt empty or very short answers
  if (!question.answer || question.answer.trim().length < MIN_ENCRYPTION_LENGTH) {
    return false;
  }

  // Check if the entire category should be encrypted
  if (categoryId && FULLY_ENCRYPTED_CATEGORIES.includes(categoryId)) {
    return question.type === 'text' || question.type === 'textarea';
  }

  // Check specific question IDs
  if (SENSITIVE_QUESTION_IDS.some(id => 
    question.originalQuestionId.toLowerCase().includes(id.toLowerCase())
  )) {
    return true;
  }

  // Check question text patterns
  if (question.question) {
    for (const pattern of SENSITIVE_QUESTION_PATTERNS) {
      if (pattern.test(question.question)) {
        return true;
      }
    }
  }

  // Check question types
  if (SENSITIVE_QUESTION_TYPES.includes(question.type.toLowerCase())) {
    return true;
  }

  // For now, don't encrypt other questions by default
  // You can modify this logic based on your requirements
  return false;
};

/**
 * Environment configuration
 */
export const ENCRYPTION_CONFIG = {
  // Whether encryption is enabled
  enabled: true,
  
  // Whether to log encryption operations (disable in production)
  debug: import.meta.env.NODE_ENV === 'development',
  
  // Encryption key source (environment variable name)
  keyEnvVar: 'VITE_ENCRYPTION_KEY',
  
  // Fallback key for development (not secure for production)
  developmentKey: 'heirkey-dev-encryption-key-change-in-production',
};
