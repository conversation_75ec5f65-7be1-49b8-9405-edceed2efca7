{"301": [{"id": "u0", "text": "Do you pay for your utility?", "type": "boolean", "required": false, "sectionId": "301A", "order": 0}, {"id": "u1", "text": "Who is your utility provider?", "type": "text", "required": false, "sectionId": "301B", "order": 1, "dependsOn": {"questionId": "u0", "value": "yes"}}, {"id": "u2", "text": "Account Number", "type": "text", "required": false, "sectionId": "301C", "order": 2, "dependsOn": {"questionId": "u0", "value": "yes"}}, {"id": "u3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "301D", "order": 3, "dependsOn": {"questionId": "u0", "value": "yes"}}, {"id": "u4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "301E", "order": 4}, {"id": "u5", "text": "Username for login", "type": "text", "required": false, "sectionId": "301F", "order": 5, "dependsOn": {"questionId": "u4", "value": "yes"}}, {"id": "u6", "text": "Password for login", "type": "text", "required": false, "sectionId": "301G", "order": 6, "dependsOn": {"questionId": "u4", "value": "yes"}}], "302": [{"id": "g0", "text": "Do you pay for your gas?", "type": "boolean", "required": false, "sectionId": "302A", "order": 0}, {"id": "g1", "text": "Who is your provider?", "type": "text", "required": false, "sectionId": "302B", "order": 1, "dependsOn": {"questionId": "g0", "value": "yes"}}, {"id": "g2", "text": "Account Number", "type": "text", "required": false, "sectionId": "302C", "order": 2, "dependsOn": {"questionId": "g0", "value": "yes"}}, {"id": "g3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "302D", "order": 3, "dependsOn": {"questionId": "g0", "value": "yes"}}, {"id": "g4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "302E", "order": 4}, {"id": "g5", "text": "Username for login", "type": "text", "required": false, "sectionId": "302F", "order": 5, "dependsOn": {"questionId": "g4", "value": "yes"}}, {"id": "g6", "text": "Password for login", "type": "text", "required": false, "sectionId": "302G", "order": 6, "dependsOn": {"questionId": "g4", "value": "yes"}}], "303": [{"id": "w0", "text": "Do you pay for your water?", "type": "boolean", "required": false, "sectionId": "303A", "order": 0}, {"id": "w1", "text": "Who is your provider?", "type": "text", "required": false, "sectionId": "303B", "order": 1, "dependsOn": {"questionId": "w0", "value": "yes"}}, {"id": "w2", "text": "Account Number", "type": "text", "required": false, "sectionId": "303C", "order": 2, "dependsOn": {"questionId": "w0", "value": "yes"}}, {"id": "w3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "303D", "order": 3, "dependsOn": {"questionId": "w0", "value": "yes"}}, {"id": "w4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "303E", "order": 4}, {"id": "w5", "text": "Username for login", "type": "text", "required": false, "sectionId": "303F", "order": 5, "dependsOn": {"questionId": "w4", "value": "yes"}}, {"id": "w6", "text": "Password for login", "type": "text", "required": false, "sectionId": "303G", "order": 6, "dependsOn": {"questionId": "w4", "value": "yes"}}], "304": [{"id": "t0", "text": "Do you pay for your trash collection?", "type": "boolean", "required": false, "sectionId": "304A", "order": 0}, {"id": "t1", "text": "What day of the week your trash is collected?", "type": "choice", "required": false, "options": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "sectionId": "304B", "order": 1, "dependsOn": {"questionId": "t0", "value": "yes"}}, {"id": "t2", "text": "Who is your provider?", "type": "text", "required": false, "sectionId": "304C", "order": 2, "dependsOn": {"questionId": "t0", "value": "yes"}}, {"id": "t3", "text": "Account Number", "type": "text", "required": false, "sectionId": "304D", "order": 3, "dependsOn": {"questionId": "t0", "value": "yes"}}, {"id": "t4", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "304E", "order": 4, "dependsOn": {"questionId": "t0", "value": "yes"}}, {"id": "t5", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "304F", "order": 5}, {"id": "t6", "text": "Username for login", "type": "text", "required": false, "sectionId": "304G", "order": 6, "dependsOn": {"questionId": "t5", "value": "yes"}}, {"id": "t7", "text": "Password for login", "type": "text", "required": false, "sectionId": "304H", "order": 7, "dependsOn": {"questionId": "t5", "value": "yes"}}], "305": [{"id": "h0", "text": "Do you have a company that service of HVAC system?", "type": "boolean", "required": false, "sectionId": "305A", "order": 0}, {"id": "h1", "text": "Who is your provider?", "type": "text", "required": false, "sectionId": "305B", "order": 1, "dependsOn": {"questionId": "h0", "value": "yes"}}, {"id": "h2", "text": "Account Number", "type": "text", "required": false, "sectionId": "305C", "order": 2, "dependsOn": {"questionId": "h0", "value": "yes"}}, {"id": "h3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "305D", "order": 3, "dependsOn": {"questionId": "h0", "value": "yes"}}, {"id": "h4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "305E", "order": 4}, {"id": "h5", "text": "Username for login", "type": "text", "required": false, "sectionId": "305F", "order": 5, "dependsOn": {"questionId": "h4", "value": "yes"}}, {"id": "h6", "text": "Password for login", "type": "text", "required": false, "sectionId": "305G", "order": 6, "dependsOn": {"questionId": "h4", "value": "yes"}}], "306": [{"id": "p0", "text": "Do you have a pest control?", "type": "boolean", "required": false, "sectionId": "306A", "order": 0}, {"id": "p1", "text": "Who is your provider?", "type": "choice", "required": false, "options": ["Terminix", "<PERSON><PERSON> (A Rollins Company)", "Rentokil (including regional brands like Ehrlich and Presto-X)", "Truly <PERSON>len", "Aptive Environmental", "Anticimex (includes brands like American Pest and Viking Pest Control)", "Massey Services", "Arrow Exterminators", "<PERSON> Pest Control", "Bulwark Exterminating", "Other (Please List)"], "sectionId": "306B", "order": 1, "dependsOn": {"questionId": "p0", "value": "yes"}}, {"id": "p1_other", "text": "Please specify your provider name", "type": "text", "required": false, "sectionId": "306B_other", "order": 1.5, "dependsOn": {"questionId": "p1", "value": "Other (Please List)"}}, {"id": "p2", "text": "Account Number", "type": "text", "required": false, "sectionId": "306C", "order": 2, "dependsOn": {"questionId": "p0", "value": "yes"}}, {"id": "p3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "306D", "order": 3, "dependsOn": {"questionId": "p0", "value": "yes"}}, {"id": "p4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "306E", "order": 4}, {"id": "p5", "text": "Username for login", "type": "text", "required": false, "sectionId": "306F", "order": 5, "dependsOn": {"questionId": "p4", "value": "yes"}}, {"id": "p6", "text": "Password for login", "type": "text", "required": false, "sectionId": "306G", "order": 6, "dependsOn": {"questionId": "p4", "value": "yes"}}], "307": [{"id": "l0", "text": "Do you have care lawn service?", "type": "boolean", "required": false, "sectionId": "307A", "order": 0}, {"id": "l1", "text": "Who is your provider?", "type": "choice", "required": false, "options": ["BrightView Landscapes", "The Davey Tree Expert Company", "TruGreen", "Bartlett Tree Experts", "Yellowstone Landscape", "Gothic Landscape", "Aspen Grove Landscape Group", "LandCare", "The Grounds Guys", "Lawn Doctor", "Other (Please List)"], "sectionId": "307B", "order": 1, "dependsOn": {"questionId": "l0", "value": "yes"}}, {"id": "l1_other", "text": "Please specify your provider name", "type": "text", "required": false, "sectionId": "307B_other", "order": 1.5, "dependsOn": {"questionId": "l1", "value": "Other (Please List)"}}, {"id": "l2", "text": "Account Number", "type": "text", "required": false, "sectionId": "307C", "order": 2, "dependsOn": {"questionId": "l0", "value": "yes"}}, {"id": "l3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "307D", "order": 3, "dependsOn": {"questionId": "l0", "value": "yes"}}, {"id": "l4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "307E", "order": 4}, {"id": "l5", "text": "Username for login", "type": "text", "required": false, "sectionId": "307F", "order": 5, "dependsOn": {"questionId": "l4", "value": "yes"}}, {"id": "l6", "text": "Password for login", "type": "text", "required": false, "sectionId": "307G", "order": 6, "dependsOn": {"questionId": "l4", "value": "yes"}}], "308": [{"id": "c0", "text": "Do you have a cable service?", "type": "boolean", "required": false, "sectionId": "308A", "order": 0}, {"id": "c1", "text": "Who is your provider?", "type": "choice", "required": false, "options": ["Xfinity (by Comcast)", "Spectrum (by Charter Communications)", "Cox Communications", "Optimum (by Altice USA)", "Mediacom", "RCN (Astound Broadband)", "Dish Network", "DIRECTV", "<PERSON><PERSON><PERSON>", "AT&T U-verse", "Other (Please List)"], "sectionId": "308B", "order": 1, "dependsOn": {"questionId": "c0", "value": "yes"}}, {"id": "c1_other", "text": "Please specify your provider name", "type": "text", "required": false, "sectionId": "308B_other", "order": 1.5, "dependsOn": {"questionId": "c1", "value": "Other (Please List)"}}, {"id": "c2", "text": "Account Number", "type": "text", "required": false, "sectionId": "308C", "order": 2, "dependsOn": {"questionId": "c0", "value": "yes"}}, {"id": "c3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "308D", "order": 3, "dependsOn": {"questionId": "c0", "value": "yes"}}, {"id": "c4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "308E", "order": 4}, {"id": "c5", "text": "Username for login", "type": "text", "required": false, "sectionId": "308F", "order": 5, "dependsOn": {"questionId": "c4", "value": "yes"}}, {"id": "c6", "text": "Password for login", "type": "text", "required": false, "sectionId": "308G", "order": 6, "dependsOn": {"questionId": "c4", "value": "yes"}}, {"id": "c7", "text": "Is your internet service the same as your cable?", "type": "boolean", "required": false, "sectionId": "308H", "order": 7, "dependsOn": {"questionId": "c0", "value": "yes"}}], "309": [{"id": "i0", "text": "Do you have a internet service?", "type": "boolean", "required": false, "sectionId": "309A", "order": 0, "dependsOn": {"questionId": "c7", "value": "no"}}, {"id": "i1", "text": "Who is your provider?", "type": "choice", "required": false, "options": ["AT&T Internet", "Xfinity (Comcast)", "Spectrum (Charter Communications)", "<PERSON><PERSON><PERSON>", "CenturyLink (Lumen Technologies)", "Cox Communications", "Frontier Communications", "Mediacom", "Windstream (Kinetic by Windstream)", "Optimum (Altice USA)", "HughesNet", "Viasat", "T-Mobile Home Internet", "Rise Broadband", "Other (Please List)"], "sectionId": "309B", "order": 1, "dependsOn": {"questionId": "i0", "value": "yes"}}, {"id": "i1_other", "text": "Please specify your provider name", "type": "text", "required": false, "sectionId": "309B_other", "order": 1.5, "dependsOn": {"questionId": "i1", "value": "Other (Please List)"}}, {"id": "i2", "text": "Account Number", "type": "text", "required": false, "sectionId": "309C", "order": 2, "dependsOn": {"questionId": "i0", "value": "yes"}}, {"id": "i3", "text": "<PERSON>", "type": "text", "required": false, "sectionId": "309D", "order": 3, "dependsOn": {"questionId": "i0", "value": "yes"}}, {"id": "i4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "309E", "order": 4}, {"id": "i5", "text": "Username for login", "type": "text", "required": false, "sectionId": "309F", "order": 5, "dependsOn": {"questionId": "i4", "value": "yes"}}, {"id": "i6", "text": "Password for login", "type": "text", "required": false, "sectionId": "309G", "order": 6, "dependsOn": {"questionId": "i4", "value": "yes"}}]}