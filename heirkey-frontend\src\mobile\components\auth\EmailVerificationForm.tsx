import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Mail, RefreshCw } from 'lucide-react';
import api from '@/services/api';
import authService from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';

export default function EmailVerificationForm() {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [resendCooldown, setResendCooldown] = useState(0);

  const inputsRef = useRef<(HTMLInputElement | null)[]>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const { setUser, setToken } = useAuth();
  
  // Get email from location state (passed from registration)
  const email = location.state?.email || '';

  // Resend cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Auto-focus first input
  useEffect(() => {
    inputsRef.current[0]?.focus();
  }, []);

  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputsRef.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputsRef.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pasted = e.clipboardData.getData('Text').slice(0, 6);
    const newOtp = [...otp];
    
    pasted.split('').forEach((char, idx) => {
      if (idx < 6 && /^\d$/.test(char)) {
        newOtp[idx] = char;
      }
    });
    
    setOtp(newOtp);
    
    // Focus the next empty input or the last input
    const nextEmptyIndex = newOtp.findIndex(val => val === '');
    const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex;
    inputsRef.current[focusIndex]?.focus();
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      setError('Please enter the complete 6-digit verification code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await api.post('/v1/api/auth/verify-email', {
        email,
        otp: otpString
      });

      const responseData = response.data as any;

      if (responseData.autoLogin && responseData.token && responseData.user) {
        // Auto-login: Store token and user data
        localStorage.setItem('token', responseData.token);
        localStorage.setItem('user', JSON.stringify(responseData.user));

        // Update AuthContext with the new token and user
        setToken(responseData.token);
        setUser(responseData.user);

        setSuccess('Email verified successfully! Logging you in...');

        // Check for subscription status before redirecting
        setTimeout(async () => {
          try {
            // Fetch latest user profile to check for pricing plan
            const latestUser = await authService.getProfile();
            if (!latestUser.pricingPlan) {
              navigate('/auth/subscribe');
            } else {
              navigate('/dashboard');
            }
          } catch (error) {
            console.error('Error fetching user profile:', error);
            // Fallback to subscription page if there's an error
            navigate('/auth/subscribe');
          }
        }, 2000);
      } else {
        // Fallback: redirect to login
        setSuccess('Email verified successfully! Redirecting to login...');

        // Redirect to login after 2 seconds
        setTimeout(() => {
          navigate('/auth/login', {
            state: {
              message: 'Email verified successfully! You can now log in.',
              email
            }
          });
        }, 2000);
      }

    } catch (error: any) {
      console.error('Email verification error:', error);
      setError(error.response?.data?.message || 'Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendCooldown > 0) return;
    
    setIsResending(true);
    setError('');

    try {
      await api.post('/v1/api/auth/resend-verification', { email });
      setSuccess('Verification code sent successfully!');
      setResendCooldown(60); // 60 second cooldown
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);
      
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      setError(error.response?.data?.message || 'Failed to resend verification code');
    } finally {
      setIsResending(false);
    }
  };

  if (!email) {
    return (
      <div className="min-h-screen bg-secondary-50 flex flex-col justify-center items-center py-12 px-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Email Required</h2>
              <p className="text-gray-600 mb-4">
                Please register first to verify your email.
              </p>
              <Button 
                onClick={() => navigate('/auth/register')}
                className="bg-[#22BBCC] text-white hover:bg-[#22BBCA]"
              >
                Go to Registration
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-secondary-50 flex flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-[#22BBCC] rounded-full flex items-center justify-center mb-4">
              <Mail className="h-6 w-6 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Verify Your Email
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              We've sent a 6-digit verification code to
            </p>
            <p className="text-sm font-medium text-[#22BBCC]">
              {email}
            </p>
          </CardHeader>
          
          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}
            
            {success && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-600">{success}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter verification code
                </label>
                <div className="flex justify-center space-x-2">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      ref={(el) => {
                        inputsRef.current[index] = el;
                      }}
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleInputChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      onPaste={index === 0 ? handlePaste : undefined}
                      className="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-md focus:ring-2 focus:ring-[#22BBCC] focus:border-transparent"
                      disabled={isLoading}
                    />
                  ))}
                </div>
              </div>
              
              <div className="flex justify-center">
                <Button
                  type="submit"
                  disabled={isLoading || otp.join('').length !== 6}
                  className="bg-[#22BBCC] text-white hover:bg-[#22BBCA] w-full"
                >
                  {isLoading ? 'Verifying...' : 'Verify Email'}
                </Button>
              </div>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Didn't receive the code?{' '}
                <button
                  type="button"
                  onClick={handleResendOTP}
                  disabled={resendCooldown > 0 || isResending}
                  className="text-[#22BBCC] hover:underline disabled:text-gray-400 disabled:no-underline"
                >
                  {isResending ? (
                    <span className="inline-flex items-center">
                      <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                      Sending...
                    </span>
                  ) : resendCooldown > 0 ? (
                    `Resend in ${resendCooldown}s`
                  ) : (
                    'Resend code'
                  )}
                </button>
              </p>
            </div>
          </CardContent>
        </Card>
        
        <div className="mt-6 flex justify-center items-center w-full">
          <ArrowLeft className="h-4 w-4 mr-1 text-gray-500" />
          <button
            type="button"
            onClick={() => navigate('/auth/register')}
            className="text-sm text-gray-600 hover:underline"
          >
            Back to registration
          </button>
        </div>
      </div>
    </>
  );
}
