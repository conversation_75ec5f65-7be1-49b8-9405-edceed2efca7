import React from 'react';
import { Field, FieldArray, useFormikContext } from 'formik';
import { Question } from './types';

export const OtherSocialMediaAccountsField = ({ groupQuestion }: { groupQuestion: any }) => {
  const { values, setFieldValue } = useFormikContext<any>();
  const accounts = Array.isArray(values.otherSocialMediaAccounts) ? values.otherSocialMediaAccounts : [];

  return (
    <FieldArray name="otherSocialMediaAccounts">
      {({ push, remove }) => (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {groupQuestion.text}
          </label>
          {accounts.map((account: any, idx: number) => (
            <div key={idx} className="mb-4 border p-4 rounded">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-xs font-medium mb-1">Service</label>
                  <select
                    name={`otherSocialMediaAccounts[${idx}].service`}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    value={account.service}
                    onChange={e => setFieldValue(`otherSocialMediaAccounts[${idx}].service`, e.target.value)}
                  >
                    <option value="">Select</option>
                    {groupQuestion.fields[0].options.map((opt: string) => (
                      <option key={opt} value={opt}>{opt}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium mb-1">Login ID</label>
                  <input
                    name={`otherSocialMediaAccounts[${idx}].loginId`}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    value={account.loginId}
                    onChange={e => setFieldValue(`otherSocialMediaAccounts[${idx}].loginId`, e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium mb-1">Password</label>
                  <input
                    name={`otherSocialMediaAccounts[${idx}].password`}
                    type="password"
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    value={account.password}
                    onChange={e => setFieldValue(`otherSocialMediaAccounts[${idx}].password`, e.target.value)}
                  />
                </div>
              </div>
              <button type="button" onClick={() => remove(idx)} className="mt-2 bg-red-500 text-white px-3 py-1 rounded">Remove</button>
            </div>
          ))}
          <button
            type="button"
            onClick={() => push({ service: '', loginId: '', password: '' })}
            className="bg-[#2BCFD5] text-white px-4 py-2 rounded mt-2"
          >
            Add another account
          </button>
        </div>
      )}
    </FieldArray>
  );
};

export const QuestionItem: React.FC<{
  question: Question;
  values: Record<string, any>;
  showPassword: boolean;
  setShowPassword: (show: boolean | ((prev: boolean) => boolean)) => void;
}> = ({ question, values, showPassword, setShowPassword }) => {
  // Check if this question should be shown based on dependencies
  const shouldShow = !question.dependsOn ||
    (values[question.dependsOn.questionId]?.toString().toLowerCase() === question.dependsOn.value.toLowerCase());

  if (!shouldShow) {
    return null;
  }

  // Add support for group type
  if ((question as any).type === 'group') {
    return <OtherSocialMediaAccountsField groupQuestion={question} />;
  }

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {question.text}
      </label>

      {question.type === 'text' && (
        <Field
          type="text"
          name={question.id}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          placeholder={question.text}
        />
      )}

      {question.type === 'password' && (
        <div className="relative">
          <Field
            name={question.id}
            type={showPassword ? "text" : "password"}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent pr-10"
            placeholder={question.text}
          />
          <button
            type="button"
            tabIndex={-1}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            onClick={() => setShowPassword((prev) => !prev)}
          >
            {showPassword ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.956 9.956 0 012.223-3.592m3.1-2.727A9.956 9.956 0 0112 5c4.478 0 8.268 2.943 9.542 7a9.973 9.973 0 01-4.293 5.411M15 12a3 3 0 11-6 0 3 3 0 016 0zm-6.364 6.364L6 18m0 0l-2-2m2 2l2-2m8 2l2-2m-2 2l-2-2" />
              </svg>
            )}
          </button>
        </div>
      )}

      {question.type === 'boolean' && (
        <div className="flex gap-2">
          {['Yes', 'No'].map(option => (
            <label
              key={option}
              className={`flex-1 py-2 px-4 border rounded-md text-center cursor-pointer min-w-[80px] max-w-xs mx-0 transition-colors duration-150 whitespace-nowrap
                ${values[question.id]?.toLowerCase() === option.toLowerCase()
                  ? 'bg-[#2BCFD5] text-white border border-[#2BCFD5] shadow'
                  : 'text-gray-500 bg-gray-100'}
              `}
            >
              <Field
                type="radio"
                name={question.id}
                value={option.toLowerCase()}
                className="hidden"
              />
              {option}
            </label>
          ))}
        </div>
      )}

      {(question.type === 'dropdown' || question.type === 'choice') && question.options && (
        <Field
          as="select"
          name={question.id}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
        >
          <option value="">Select an option</option>
          {question.options.map(option => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </Field>
      )}
    </div>
  );
}; 