import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Field, Form, ErrorMessage } from "formik";
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import homeDocuments from '@/data/homeDocuments.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/homeDocumentsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

const subCategoryId = '308'; // Cable
const questions = homeDocuments[subCategoryId] || [];

function splitCableSteps(questions: any[]) {
  // Step 1: Do you have a cable service? (c0)
  const step1 = questions.filter(q => q.id === "c0");
  // Step 2: Provider, Account Number, Bill Amount (c1, c1_other, c2, c3)
  const step2 = questions.filter(q => ["c1", "c1_other", "c2", "c3"].includes(q.id));
  // Step 3: Do you pay online? (c4), login info (c5, c6)
  const step3 = questions.filter(q => ["c4", "c5", "c6"].includes(q.id));
  return [step1, step2, step3];
}

type FormValues = Record<string, string | string[]>;

export default function CablePage() {
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const fromReview = params.get('fromReview');
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const [step, setStep] = useState(0);
  const [formError, setError] = useState<string | null>(null);
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);

  // Get data from Redux store using memoized selectors
  const storeQuestions = useAppSelector(selectQuestionsBySubcategoryId(subCategoryId));
  const loading = useAppSelector(selectLoading);
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('308A'));
  const reduxError = useAppSelector(selectError);

  // Find the userInput for this subcategory
  const userInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '308A');
  const existingInputIdFromStore = userInput?._id || null;

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Prepare form values
  const initialValues = Object.keys(savedAnswers).length > 0
    ? savedAnswers
    : Object.fromEntries(questions.map(q => [q.id, ""]));
  const steps = splitCableSteps(questions);
  const currentStepQuestions = steps[step] || [];
  const tabs = categoryTabsConfig.homedocuments;

  function validate(values: FormValues) {
    const errors: Record<string, string> = {};
    // Removed required field validation - all fields are now optional
    return errors;
  }

  function getCurrentStepRequiredFields(values: FormValues) {
    return currentStepQuestions.filter(q =>
      q.required && (!q.dependsOn || values[q.dependsOn.questionId] === q.dependsOn.value)
    );
  }

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <>
      <GradiantHeader title="Home Documents" showAvatar={true} />
      <div style={{ padding: 16 }}>
        {/* Tab Bar */}
        <div className="flex gap-3 mb-4 bg-gray-50 rounded-2xl p-1 overflow-x-auto">
          {tabs.map(tab => {
            const isActive = tab.label === "Cable";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-md font-medium " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500")
                }
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        <Formik
          initialValues={initialValues}
          validate={validate}
          enableReinitialize={true}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // If user doesn't have cable service, only save that answer
              if (values["c0"] === "no") {
                const userData = {
                  userId: user.id,
                  categoryId: generateObjectId(),
                  originalCategoryId: '2',
                  subCategoryId: generateObjectId(),
                  originalSubCategoryId: '308A',
                  answersBySection: [{
                    originalSectionId: "308A",
                    isCompleted: true,
                    answers: [{
                      index: 0,
                      originalQuestionId: "c0",
                      question: "Do you have a cable service?",
                      type: "boolean",
                      answer: "no"
                    }]
                  }]
                };

                if (existingInputId) {
                  await dispatch(updateUserInput({
                    id: existingInputId,
                    userData
                  })).unwrap();
                } else {
                  await dispatch(saveUserInput(userData)).unwrap();
                }
                if (fromReview) {
                  navigate('/category/homedocuments/review');
                } else {
                  navigate('/category/homedocuments/internet');
                }
                setSubmitting(false);
                return;
              }

              // Group answers by sectionId
              const answersBySection: Record<string, any[]> = {};
              questions.forEach(q => {
                const value = values[q.id];
                if (value !== "" && value !== undefined) {
                  if (!answersBySection[q.sectionId]) answersBySection[q.sectionId] = [];
                  answersBySection[q.sectionId].push({
                    index: q.order,
                    originalQuestionId: q.id,
                    question: q.text,
                    type: q.type,
                    answer: Array.isArray(value) ? value.join(', ') : value
                  });
                }
              });

              const formattedAnswersBySection = Object.entries(answersBySection).map(
                ([sectionId, answers]) => ({
                  originalSectionId: sectionId,
                  isCompleted: true,
                  answers
                })
              );

              const userData = {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '2',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '308A',
                answersBySection: formattedAnswersBySection
              };

              if (existingInputId) {
                await dispatch(updateUserInput({
                  id: existingInputId,
                  userData
                })).unwrap();
              } else {
                await dispatch(saveUserInput(userData)).unwrap();
              }
              if (fromReview) {
                navigate('/category/homedocuments/review');
              } else {
                navigate('/category/homedocuments/internet');
              }
              setSubmitting(false);
            } catch (err) {
              setError("Failed to save your answers. Please try again.");
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting, setTouched, validateForm, handleSubmit }) => {
            let visibleQuestions = currentStepQuestions.filter(q => {
              if (!q.dependsOn) return true;
              return values[q.dependsOn.questionId] === q.dependsOn.value;
            });

            const requiredFields = getCurrentStepRequiredFields(values);

            const handleNext = async () => {
              const touchedFields = Object.fromEntries(visibleQuestions.map(q => [q.id, true]));
              setTouched(touchedFields, true);
              const formErrors = await validateForm();
              const stepHasError = visibleQuestions.some(q => formErrors[q.id]);
              if (!stepHasError) {
                if (step === 0 && values["c0"] === "no") {
                  handleSubmit();
                } else {
                  setStep(s => s + 1);
                }
              }
            };

            const handleSave = async () => {
              const touchedFields = Object.fromEntries(visibleQuestions.map(q => [q.id, true]));
              setTouched(touchedFields, true);
              const formErrors = await validateForm();
              const stepHasError = visibleQuestions.some(q => formErrors[q.id]);
              if (!stepHasError) {
                if (step === 0 && values["c0"] === "no") {
                  handleSubmit();
                } else {
                  handleSubmit();
                }
              }
            };

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Home Documents: <span className="text-[#2BCFD5]">Cable</span>
                    </p>
                    <CircularProgress
                      value={step + 1}
                      max={values["c0"] === "no" ? 1 : steps.length}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>

                <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                  {visibleQuestions.map(q => (
                    <div key={q.id}>
                      <label className="block font-medium text-gray-700 mb-2">
                        {q.text}{q.required && " *"}
                      </label>
                      {q.type === "boolean" ? (
                        <div className="flex space-x-4">
                          {["yes", "no"].map(option => (
                            <label
                              key={option}
                              className={
                                "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer transition-all " +
                                (values[q.id] === option
                                  ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                  : "bg-gray-50 text-gray-800 border-gray-200 hover:bg-[#25b6bb] hover:text-white")
                              }
                            >
                              <Field type="radio" name={q.id} value={option} className="hidden" />
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </label>
                          ))}
                        </div>
                      ) : q.type === "choice" ? (
                        <div className="space-y-2">
                          <Field
                            as="select"
                            name={q.id}
                            className="w-full border rounded-lg px-3 py-2 bg-white"
                          >
                            <option value="">Select an option</option>
                            {q.options.map((option: string) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </Field>
                          {values[q.id] === "Other (Please List)" && (
                            <Field
                              name={`${q.id}_other`}
                              type="text"
                              placeholder="Please specify your provider name"
                              className="w-full border rounded-lg px-3 py-2 mt-2"
                            />
                          )}
                        </div>
                      ) : (
                        <Field
                          name={q.id}
                          type={
                            q.text.toLowerCase().includes("password") ? "password"
                            : q.text.toLowerCase().includes("account number") ? "number"
                            : q.text.toLowerCase().includes("bill amount") ? "number"
                            : "text"
                          }
                          className="w-full border rounded-lg px-3 py-2"
                        />
                      )}
                      <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                      {q.id.endsWith('_other') && (
                        <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                      )}
                    </div>
                  ))}
                </div>

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep(s => s - 1)}
                    disabled={step === 0}
                    className="text-[#2BCFD5] underline disabled:opacity-50"
                  >
                    ← Back
                  </button>
                  {step < steps.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleSave}
                      disabled={isSubmitting}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Save
                    </button>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
