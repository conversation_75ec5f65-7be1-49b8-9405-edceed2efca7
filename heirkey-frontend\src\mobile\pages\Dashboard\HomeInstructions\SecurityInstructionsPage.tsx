import { Formik, Field, Form, ErrorMessage } from "formik";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import Footer from "@/mobile/components/layout/Footer";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAuth } from '@/contexts/AuthContext';
import { useState, useEffect } from "react";
import { Alert, AlertDescription } from '@/components/ui/alert';
import ScrollToQuestion from '@/mobile/components/dashboard/HomeInstructions/ScrollToQuestion';
import { castToQuestionType } from '@/mobile/utils/questionUtils';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput,
  selectUserInputsBySubcategoryId,
  selectQuestionsBySubcategoryId,
  selectLoading,
  selectError
} from '@/store/slices/homeInstructionsSlice';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

const initialValues = {
  s1: "",
  s2: "",
  s3: "",
  s4: "",
  s5: "",
  s6: "",
  s7: "",
  s8: "",
};

export default function SecurityInstructionsPage() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { categoryName } = useParams<{ categoryName: string }>();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, any>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Get data from Redux store
  const securityQuestions = useAppSelector((state) => selectQuestionsBySubcategoryId('104')(state));
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('104')(state));
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Cast questions to the correct type - memoize to prevent recalculation on every render
  const typedQuestions = useState(() => castToQuestionType(securityQuestions))[0];

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in SecurityInstructionsPage component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in SecurityInstructionsPage component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Handle target question in a separate effect
  useEffect(() => {
    if (targetQuestionId && typedQuestions.length > 0) {
      const questionElement = document.getElementById(`question-${targetQuestionId}`);
      if (questionElement) {
        questionElement.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [targetQuestionId]);

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Home Instructions" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  return (
    <>
      <GradiantHeader title="Home Instructions" showAvatar={true} />
      <div style={{ padding: 16 }}>
        {/* Tab Bar */}
        <div className="flex gap-2 mb-4 bg-gray-50 rounded-lg p-1">
          {categoryTabsConfig.homeinstructions.map(tab => {
            const isActive = tab.label === "Security";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-md font-medium " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500 hover:bg-[#25b6bb] hover:text-white")
                }
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : initialValues}
          validate={values => {
            const errors: Record<string, string> = {};
            // Removed required field validation - all fields are now optional
            return errors;
          }}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              console.log("Security Instructions Submitted", values);

              if (!user || !user.id) {
                console.error('User not authenticated');
                setError('You must be logged in to save answers');
                return;
              }

              const answers = Object.entries(values)
                .filter(([_, value]) => value !== "")
                .map(([key, value], index) => {
                  const question = typedQuestions.find(q => q.id === key);
                  return {
                    index,
                    originalQuestionId: key,
                    question: question?.text || key,
                    type: question?.type || "text",
                    answer: value
                  };
                });

              const formattedAnswersBySection = [{
                originalSectionId: '104A',
                isCompleted: true,
                answers
              }];

              if (existingInputId) {
                try {
                  await dispatch(updateUserInput({
                    id: existingInputId,
                    userData: {
                      userId: user.id,
                      categoryId: generateObjectId(),
                      originalCategoryId: '1',
                      subCategoryId: generateObjectId(),
                      originalSubCategoryId: '104',
                      answersBySection: formattedAnswersBySection
                    } as UserInput
                  })).unwrap();
                  console.log('Successfully updated record');
                } catch (error) {
                  console.error('Error updating record:', error);
                  setExistingInputId(null);
                }
              }

              if (!existingInputId) {
                const userData: Omit<UserInput, '_id'> = {
                  userId: user.id,
                  categoryId: generateObjectId(),
                  originalCategoryId: '1',
                  subCategoryId: generateObjectId(),
                  originalSubCategoryId: '104',
                  answersBySection: formattedAnswersBySection
                };

                const result = await dispatch(saveUserInput(userData)).unwrap();
                if (result && result._id) {
                  setExistingInputId(result._id);
                }
              }

              navigate(`/category/homeinstructions/other`);
              return;
            } catch (err: any) {
              console.error('Error saving security instructions:', err);
              setError(err.message || 'Failed to save your answers. Please try again.');
              setSubmitting(false);
              navigate(`/category/homeinstructions/other`);
              return;
            }
          }}
        >
          {({ values, isSubmitting, setFieldValue }) => (
            <Form>
              <div className="bg-gray-50 p-5 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <p className="text-lg font-semibold">
                    Home Instructions: <span className="text-[#2BCFD5]">Security</span>
                  </p>
                  <CircularProgress 
                    value={1} 
                    max={1} 
                    size={40} 
                    stroke={3}
                    color="#2BCFD5"
                  />
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-xl shadow-sm border mt-4">
                <ScrollToQuestion questions={typedQuestions}>
                  {(refs) => (
                    <>
                      {/* Security System Question */}
                      <div
                        id={`question-${typedQuestions[0]?.id}`}
                        ref={(el: HTMLDivElement | null) => {
                          if (typedQuestions[0]) {
                            refs[typedQuestions[0].id] = el;
                          }
                        }}
                      >
                        <label className="block font-medium text-gray-700 mb-2">
                          {typedQuestions[0]?.text}
                        </label>
                        <div className="flex gap-2 mb-4">
                          {["yes", "no"].map(opt => (
                            <button
                              key={opt}
                              type="button"
                              className={
                                "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                (values.s1 === opt
                                  ? "bg-[#2BCFD5] text-white"
                                  : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                              }
                              onClick={() => setFieldValue("s1", opt)}
                            >
                              {opt.charAt(0).toUpperCase() + opt.slice(1)}
                            </button>
                          ))}
                        </div>
                        <ErrorMessage name="s1" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      {/* Company Selection */}
                      {values.s1 === "yes" && typedQuestions[1] && (
                        <div
                          id={`question-${typedQuestions[1]?.id}`}
                          ref={(el: HTMLDivElement | null) => {
                            if (typedQuestions[1]) {
                              refs[typedQuestions[1].id] = el;
                            }
                          }}
                          className="mt-4"
                        >
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[1]?.text}
                          </label>
                          <Field
                            as="select"
                            name="s2"
                            className="w-full border rounded-lg px-3 py-2"
                          >
                            <option value="">Select a company</option>
                            {typedQuestions[1]?.options?.map((option: string) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </Field>
                          <ErrorMessage name="s2" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}

                      {/* Other Company Specification */}
                      {values.s1 === "yes" && values.s2 === "Other" && (
                        <div className="mt-4">
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[2]?.text}
                          </label>
                          <Field
                            type="text"
                            name="s3"
                            className="w-full border rounded-lg px-3 py-2"
                          />
                          <ErrorMessage name="s3" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}

                      {/* Account Number */}
                      {values.s1 === "yes" && (
                        <div className="mt-4">
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[3]?.text}
                          </label>
                          <Field
                            type="text"
                            name="s4"
                            className="w-full border rounded-lg px-3 py-2"
                          />
                          <ErrorMessage name="s4" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}

                      {/* Online Payment Question */}
                      {values.s1 === "yes" && (
                        <div className="mt-4">
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[4]?.text}
                          </label>
                          <div className="flex gap-2 mb-4">
                            {["yes", "no"].map(opt => (
                              <button
                                key={opt}
                                type="button"
                                className={
                                  "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                  (values.s5 === opt
                                    ? "bg-[#2BCFD5] text-white"
                                    : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                }
                                onClick={() => setFieldValue("s5", opt)}
                              >
                                {opt.charAt(0).toUpperCase() + opt.slice(1)}
                              </button>
                            ))}
                          </div>
                          <ErrorMessage name="s5" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}

                      {/* Username */}
                      {values.s1 === "yes" && values.s5 === "yes" && (
                        <div className="mt-4">
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[5]?.text}
                          </label>
                          <Field
                            type="text"
                            name="s6"
                            className="w-full border rounded-lg px-3 py-2"
                          />
                          <ErrorMessage name="s6" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}

                      {/* Password */}
                      {values.s1 === "yes" && values.s5 === "yes" && (
                        <div className="mt-4">
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[6]?.text}
                          </label>
                          <Field
                            type="password"
                            name="s7"
                            className="w-full border rounded-lg px-3 py-2"
                          />
                          <ErrorMessage name="s7" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}

                      {/* Security Code */}
                      {values.s1 === "yes" && (
                        <div className="mt-4">
                          <label className="block font-medium text-gray-700 mb-2">
                            {typedQuestions[7]?.text}
                          </label>
                          <Field
                            type="text"
                            name="s8"
                            className="w-full border rounded-lg px-3 py-2"
                          />
                          <ErrorMessage name="s8" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      )}
                    </>
                  )}
                </ScrollToQuestion>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold mt-6 hover:bg-[#25b6bb]"
                >
                  Save
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
