import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form } from "formik";
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import socialMediaData from '@/data/socialMedia.json';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { Question as StoreQuestion } from '@/store/slices/socialMediaSlice';
import { Question as FormQuestion } from '@/mobile/components/dashboard/SocialMedia/types';
import ScrollToQuestion from '@/mobile/components/dashboard/SocialMedia/ScrollToQuestion';
import { QuestionItem } from '@/mobile/components/dashboard/SocialMedia/FormFields';
import { useAuth } from '@/contexts/AuthContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput,
  selectUserInputsBySubcategoryId,
  selectLoading,
  selectError,
  SectionAnswers
} from '@/store/slices/socialMediaSlice';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Map store question types to form question types
const mapQuestionType = (question: StoreQuestion): FormQuestion => {
  if (question.type === 'group') {
    return question as FormQuestion;
  }
  return {
    ...question,
    type: question.type === 'choice' ? 'dropdown' : 
      question.type === 'textarea' ? 'text' :
      question.type === 'number' ? 'text' :
      question.type
  };
};

const sectionQuestions: FormQuestion[] = (socialMediaData["206"] as StoreQuestion[])
  .filter(q => q.sectionId === "206D")
  .map(mapQuestionType);

const initialValues: Record<string, string> = sectionQuestions.reduce((acc, q) => ({ ...acc, [q.id]: "" }), {});

function getVisibleQuestions(questions: FormQuestion[], values: Record<string, string>) {
  return questions.filter(q => {
    if (!q.dependsOn) return true;
    return values[q.dependsOn.questionId] === q.dependsOn.value;
  });
}

export default function OtherAccountsPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('206D')(state));
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in OtherAccountsPage component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in OtherAccountsPage component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];

      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        const stringValues: Record<string, any> = {};
        Object.entries(formValues).forEach(([key, value]) => {
          if (key === 'otherSocialMediaAccounts') {
            stringValues[key] = Array.isArray(value) ? value : [];
          } else {
            stringValues[key] = Array.isArray(value) ? value.join(', ') : String(value);
          }
        });
        setSavedAnswers(stringValues);
      } else if (!existingInputId && userInput._id) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        const stringValues: Record<string, any> = {};
        Object.entries(formValues).forEach(([key, value]) => {
          if (key === 'otherSocialMediaAccounts') {
            stringValues[key] = Array.isArray(value) ? value : [];
          } else {
            stringValues[key] = Array.isArray(value) ? value.join(', ') : String(value);
          }
        });
        setSavedAnswers(stringValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Show loading state if data is being fetched
  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Social Media and Phone" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  // Tab navigation
  const tabs = categoryTabsConfig.socialmedia;

  return (
    <>
      <GradiantHeader title="Social Media and Phone" showAvatar={true} />
      <div className="container mx-auto px-2 py-4 max-w-md">
        {/* Tab Bar with horizontal scroll if overflow */}
        <div className="flex gap-2 mb-4 bg-gray-50 rounded-2xl p-1 overflow-x-auto whitespace-nowrap scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-gray-100" style={{ WebkitOverflowScrolling: 'touch' }}>
          {tabs.map(tab => {
            const isActive = tab.label === "Other social";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-md font-medium min-w-[120px] max-w-xs px-4 mx-0 " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500")
                }
                style={{ flexShrink: 0 }}
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : initialValues}
          validateOnChange={false}
          validateOnBlur={false}
          validate={(values: Record<string, string>) => {
            const errors: Record<string, string> = {};
            // Removed required field validation - all fields are now optional
            return errors;
          }}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              setFormError(null);

              if (!user || !user.id) {
                setFormError('You must be logged in to save answers');
                return;
              }

              let answers: any[] = [];

              // 1. Add all non-group answers as before
              sectionQuestions.forEach((q, index) => {
                if ((q as any).fields) return; // skip group type if present
                if (q.dependsOn && values[q.dependsOn.questionId] !== q.dependsOn.value) {
                  return;
                }
                answers.push({
                  index,
                  originalQuestionId: q.id,
                  question: q.text,
                  type: q.type === 'password' ? 'text' : q.type === 'dropdown' ? 'choice' : q.type,
                  answer: values[q.id] || ''
                });
              });

              // 2. Add each account in otherSocialMediaAccounts as a set of answers
              if (Array.isArray((values as any).otherSocialMediaAccounts)) {
                (values as any).otherSocialMediaAccounts.forEach((account: any) => {
                  if (account.service) {
                    answers.push({
                      index: answers.length,
                      originalQuestionId: 's13',
                      question: 'Which Service (Other Social Media)',
                      type: 'choice',
                      answer: account.service
                    });
                  }
                  if (account.loginId) {
                    answers.push({
                      index: answers.length,
                      originalQuestionId: 's14',
                      question: 'Other Social Media Login ID',
                      type: 'text',
                      answer: account.loginId
                    });
                  }
                  if (account.password) {
                    answers.push({
                      index: answers.length,
                      originalQuestionId: 's15',
                      question: 'Other Social Media Password',
                      type: 'text',
                      answer: account.password
                    });
                  }
                });
              }

              const formattedAnswersBySection = [{
                originalSectionId: '206D',
                isCompleted: true,
                answers
              }];

              if (existingInputId) {
                try {
                  await dispatch(updateUserInput({
                    id: existingInputId,
                    userData: {
                      userId: user.id,
                      categoryId: generateObjectId(),
                      originalCategoryId: '6',
                      subCategoryId: generateObjectId(),
                      originalSubCategoryId: '206D',
                      answersBySection: formattedAnswersBySection
                    } as UserInput
                  })).unwrap();

                  navigate('/category/socialmedia/cellphone');
                } catch (error) {
                  console.error('Error updating record:', error);
                  setExistingInputId(null);
                }
              }

              if (!existingInputId) {
                const userData: Omit<UserInput, '_id'> = {
                  userId: user.id,
                  categoryId: generateObjectId(),
                  originalCategoryId: '6',
                  subCategoryId: generateObjectId(),
                  originalSubCategoryId: '206D',
                  answersBySection: formattedAnswersBySection as SectionAnswers[]
                };

                await dispatch(saveUserInput(userData)).unwrap();
                navigate('/category/socialmedia/cellphone');
              }
            } catch (error) {
              console.error('Error saving answers:', error);
              setFormError('Failed to save answers. Please try again.');
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting }: { values: Record<string, string>; isSubmitting: boolean }) => {
            const visibleQuestions = getVisibleQuestions(sectionQuestions, values);
            const answeredCount = visibleQuestions.filter(q => values[q.id] && values[q.id].trim() !== '').length;
            const totalCount = visibleQuestions.length;
            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-2 px-4">
                  <div className="flex items-center justify-between">
                    <div className="text-lg font-semibold ">Social & Phone: <span className="text-[#2BCFD5]">Other Accounts</span></div>
                    <CircularProgress value={answeredCount} max={totalCount} size={40} stroke={3} color="#2BCFD5" />
                  </div>
                </div>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4 px-4">
                  <ScrollToQuestion questions={visibleQuestions}>
                    {(refs) => (
                      <>
                        {visibleQuestions.map((question) => (
                          <div key={question.id} ref={(el) => {
                            if (el) refs[question.id] = el;
                          }}>
                            <QuestionItem
                              question={question}
                              values={values}
                              showPassword={showPassword}
                              setShowPassword={setShowPassword}
                            />
                          </div>
                        ))}
                      </>
                    )}
                  </ScrollToQuestion>
                  <button
                    type="submit"
                    className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb] w-full mt-2"
                  >
                    Save
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
