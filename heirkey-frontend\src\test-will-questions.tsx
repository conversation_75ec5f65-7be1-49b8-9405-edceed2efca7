import React from 'react';
import { useAppSelector } from '@/store';
import { selectQuestionsBySubcategoryId } from '@/store/slices/willInstructionsSlice';

const TestWillQuestions: React.FC = () => {
  const questions105 = useAppSelector(selectQuestionsBySubcategoryId('105'));
  const questions105B = useAppSelector(selectQuestionsBySubcategoryId('105B'));
  const questions105A = useAppSelector(selectQuestionsBySubcategoryId('105A'));

  console.log('Test - questions105:', questions105);
  console.log('Test - questions105B:', questions105B);
  console.log('Test - questions105A:', questions105A);

  return (
    <div style={{ padding: '20px' }}>
      <h1>Will Questions Test</h1>
      <div>
        <h2>Questions 105 (all): {questions105.length}</h2>
        <ul>
          {questions105.map(q => (
            <li key={q.id}>{q.id}: {q.text} (Section: {q.sectionId})</li>
          ))}
        </ul>
      </div>
      <div>
        <h2>Questions 105B (Location - 105A + 105B): {questions105B.length}</h2>
        <ul>
          {questions105B.map(q => (
            <li key={q.id}>{q.id}: {q.text} (Section: {q.sectionId})</li>
          ))}
        </ul>
      </div>
      <div>
        <h2>Questions 105A (Legal - 105C): {questions105A.length}</h2>
        <ul>
          {questions105A.map(q => (
            <li key={q.id}>{q.id}: {q.text} (Section: {q.sectionId})</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default TestWillQuestions;
