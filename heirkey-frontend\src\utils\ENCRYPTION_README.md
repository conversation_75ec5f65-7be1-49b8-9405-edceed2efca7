# Heirkey Frontend Encryption System

This document explains how the frontend encryption system works in Heirkey and how to use it.

## Overview

The Heirkey frontend now includes a comprehensive encryption system that automatically encrypts sensitive user answers before sending them to the backend and decrypts them when fetching data. This provides an additional layer of security for sensitive user information.

## How It Works

### 1. Automatic Encryption/Decryption
- **Encryption**: Sensitive answers are automatically encrypted in the frontend before being sent to the backend
- **Decryption**: Encrypted answers are automatically decrypted when fetched from the backend
- **Transparency**: The encryption/decryption process is transparent to the UI components

### 2. Configuration-Based
- Encryption rules are defined in `src/config/encryptionConfig.ts`
- You can configure which questions, categories, or patterns should be encrypted
- Easy to add new sensitive question types or patterns

### 3. Web Crypto API
- Uses the browser's native Web Crypto API for AES-256-GCM encryption
- Secure key derivation using PBKDF2
- Compatible with backend encryption format

## Files Structure

```
src/
├── utils/
│   ├── encryptionUtils.ts          # Core encryption functions
│   └── ENCRYPTION_README.md        # This documentation
├── config/
│   └── encryptionConfig.ts         # Encryption configuration
└── services/
    └── userInputService.ts         # Updated to use encryption
```

## Configuration

### Environment Variables

Set the encryption key in your environment:

```bash
# .env.local or .env
VITE_ENCRYPTION_KEY=your-secure-encryption-key-here
```

**Important**: Use a strong, unique encryption key in production!

### Encryption Rules

Edit `src/config/encryptionConfig.ts` to configure which data should be encrypted:

```typescript
// Question patterns that should be encrypted
export const SENSITIVE_QUESTION_PATTERNS = [
  /password/i,
  /pin/i,
  /ssn/i,
  // Add more patterns...
];

// Specific question IDs that should be encrypted
export const SENSITIVE_QUESTION_IDS = [
  's3', 's6', 's9', // Social media passwords
  'bank_account',
  // Add more IDs...
];

// Categories where all text answers should be encrypted
export const FULLY_ENCRYPTED_CATEGORIES = [
  '6', // Social Media category
  // Add more categories...
];
```

## Usage

### Automatic Usage (Recommended)

The encryption system works automatically with the existing Redux slices and userInputService. No changes needed in your components:

```typescript
// This automatically encrypts sensitive data before saving
dispatch(saveUserInput(userData));

// This automatically decrypts data when fetching
dispatch(fetchUserInputs({ userId, categoryId }));
```

### Manual Usage

If you need to manually encrypt/decrypt data:

```typescript
import { encrypt, decrypt, encryptAnswer, decryptAnswer } from '@/utils/encryptionUtils';

// Encrypt a string
const encryptedText = await encrypt('sensitive data');

// Decrypt a string
const decryptedText = await decrypt(encryptedText);

// Encrypt an answer object
const encryptedAnswer = await encryptAnswer({
  originalQuestionId: 's3',
  question: 'Password',
  type: 'text',
  answer: 'mypassword123',
  index: 0
});

// Decrypt an answer object
const decryptedAnswer = await decryptAnswer(encryptedAnswer);
```

## Security Considerations

### 1. Key Management
- **Development**: Uses session-based keys (not secure for production)
- **Production**: Must set `VITE_ENCRYPTION_KEY` environment variable
- **Recommendation**: Use a secure key management system in production

### 2. What Gets Encrypted
- Only sensitive answers based on configuration rules
- Empty or very short answers are not encrypted
- Non-sensitive questions remain unencrypted for performance

### 3. Backward Compatibility
- System can handle both encrypted and unencrypted data
- Gracefully falls back if decryption fails
- Existing unencrypted data remains accessible

## Adding New Sensitive Questions

To mark new questions as sensitive for encryption:

1. **By Pattern**: Add regex patterns to `SENSITIVE_QUESTION_PATTERNS`
2. **By ID**: Add question IDs to `SENSITIVE_QUESTION_IDS`
3. **By Category**: Add category IDs to `FULLY_ENCRYPTED_CATEGORIES`
4. **By Type**: Add question types to `SENSITIVE_QUESTION_TYPES`

Example:
```typescript
// Add to encryptionConfig.ts
export const SENSITIVE_QUESTION_IDS = [
  ...existing_ids,
  'new_sensitive_question_id',
  'another_sensitive_id'
];
```

## Troubleshooting

### Common Issues

1. **Encryption Key Warning**
   ```
   ⚠️ Using session-based encryption key. Set VITE_ENCRYPTION_KEY for production.
   ```
   **Solution**: Set the `VITE_ENCRYPTION_KEY` environment variable

2. **Decryption Failures**
   ```
   ❌ Frontend decryption failed
   ```
   **Solution**: Check if the encryption key matches between sessions

3. **Type Errors**
   **Solution**: The system uses `any` types in some places for flexibility with the existing codebase

### Debug Mode

Enable debug logging in development:
```typescript
// In encryptionConfig.ts
export const ENCRYPTION_CONFIG = {
  debug: true, // Enable debug logging
  // ...
};
```

## Performance Considerations

- Encryption/decryption is asynchronous and may add slight delays
- Only sensitive questions are encrypted to minimize performance impact
- Consider the trade-off between security and performance for your use case

## Future Enhancements

Potential improvements for the encryption system:

1. **Key Rotation**: Implement automatic key rotation
2. **Field-Level Encryption**: More granular encryption controls
3. **Compression**: Compress data before encryption
4. **Caching**: Cache decrypted data for better performance
5. **Audit Logging**: Log encryption/decryption operations
