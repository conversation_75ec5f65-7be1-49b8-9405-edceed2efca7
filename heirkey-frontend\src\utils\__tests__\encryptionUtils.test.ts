/**
 * Tests for encryption utilities
 * Run with: npm test encryptionUtils.test.ts
 */

import { encrypt, decrypt, encryptAnswer, decryptAnswer } from '../encryptionUtils';

// Mock environment for testing
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_ENCRYPTION_KEY: 'test-encryption-key-for-testing-only-must-be-same-as-backend',
    NODE_ENV: 'test'
  }
});

describe('Encryption Utils', () => {
  describe('Basic encryption/decryption', () => {
    test('should encrypt and decrypt a string correctly', async () => {
      const originalText = 'This is sensitive data';
      
      const encrypted = await encrypt(originalText);
      expect(encrypted).not.toBe(originalText);
      expect(encrypted.length).toBeGreaterThan(originalText.length);
      
      const decrypted = await decrypt(encrypted);
      expect(decrypted).toBe(originalText);
    });

    test('should handle empty strings', async () => {
      const emptyString = '';
      const encrypted = await encrypt(emptyString);
      expect(encrypted).toBe(emptyString);
      
      const decrypted = await decrypt(encrypted);
      expect(decrypted).toBe(emptyString);
    });

    test('should handle non-string inputs gracefully', async () => {
      const nullValue = null as any;
      const undefinedValue = undefined as any;
      
      expect(await encrypt(nullValue)).toBe(nullValue);
      expect(await encrypt(undefinedValue)).toBe(undefinedValue);
      expect(await decrypt(nullValue)).toBe(nullValue);
      expect(await decrypt(undefinedValue)).toBe(undefinedValue);
    });
  });

  describe('Answer encryption/decryption', () => {
    test('should encrypt sensitive password answers', async () => {
      const passwordAnswer = {
        index: 0,
        originalQuestionId: 's3',
        question: 'What is your Instagram password?',
        type: 'text',
        answer: 'mySecretPassword123',
        is_encrypted: false
      };

      const encrypted = await encryptAnswer(passwordAnswer, '6'); // Social media category
      
      expect(encrypted.is_encrypted).toBe(true);
      expect(encrypted.answer).not.toBe(passwordAnswer.answer);
      expect(encrypted.originalQuestionId).toBe(passwordAnswer.originalQuestionId);
      
      const decrypted = await decryptAnswer(encrypted);
      expect(decrypted.is_encrypted).toBe(false);
      expect(decrypted.answer).toBe(passwordAnswer.answer);
    });

    test('should not encrypt non-sensitive answers', async () => {
      const normalAnswer = {
        index: 0,
        originalQuestionId: 'q1',
        question: 'What is your favorite color?',
        type: 'text',
        answer: 'Blue',
        is_encrypted: false
      };

      const result = await encryptAnswer(normalAnswer);
      
      expect(result.is_encrypted).toBeFalsy();
      expect(result.answer).toBe(normalAnswer.answer);
    });

    test('should not re-encrypt already encrypted answers', async () => {
      const alreadyEncrypted = {
        index: 0,
        originalQuestionId: 's3',
        question: 'Password',
        type: 'text',
        answer: 'already-encrypted-data',
        is_encrypted: true
      };

      const result = await encryptAnswer(alreadyEncrypted);
      
      expect(result.answer).toBe(alreadyEncrypted.answer);
      expect(result.is_encrypted).toBe(true);
    });

    test('should handle short answers correctly', async () => {
      const shortAnswer = {
        index: 0,
        originalQuestionId: 's3',
        question: 'PIN',
        type: 'text',
        answer: 'No', // Too short to encrypt
        is_encrypted: false
      };

      const result = await encryptAnswer(shortAnswer);
      
      // Should not encrypt very short answers
      expect(result.is_encrypted).toBeFalsy();
      expect(result.answer).toBe(shortAnswer.answer);
    });
  });

  describe('Error handling', () => {
    test('should handle decryption of non-encrypted data gracefully', async () => {
      const plainText = 'This is not encrypted';
      
      // Should return the original text if it's not encrypted
      const result = await decrypt(plainText);
      expect(result).toBe(plainText);
    });

    test('should handle malformed encrypted data', async () => {
      const malformedData = 'this-is-not-valid-base64-encrypted-data';
      
      // Should return the original data if decryption fails
      const result = await decrypt(malformedData);
      expect(result).toBe(malformedData);
    });
  });

  describe('Configuration-based encryption', () => {
    test('should encrypt social media passwords', async () => {
      const socialMediaAnswer = {
        index: 0,
        originalQuestionId: 's6', // Facebook password
        question: 'Facebook password',
        type: 'text',
        answer: 'myFacebookPassword',
        is_encrypted: false
      };

      const result = await encryptAnswer(socialMediaAnswer, '6');
      expect(result.is_encrypted).toBe(true);
    });

    test('should encrypt based on question patterns', async () => {
      const ssnAnswer = {
        index: 0,
        originalQuestionId: 'personal1',
        question: 'What is your Social Security Number?',
        type: 'text',
        answer: '***********',
        is_encrypted: false
      };

      const result = await encryptAnswer(ssnAnswer);
      expect(result.is_encrypted).toBe(true);
    });
  });
});

// Helper function to run tests manually in browser console
(window as any).testEncryption = async () => {
  console.log('🧪 Testing encryption system...');
  
  try {
    // Test basic encryption
    const testData = 'Hello, this is sensitive data!';
    const encrypted = await encrypt(testData);
    const decrypted = await decrypt(encrypted);
    
    console.log('✅ Basic encryption test passed');
    console.log('Original:', testData);
    console.log('Encrypted:', encrypted);
    console.log('Decrypted:', decrypted);
    console.log('Match:', testData === decrypted);
    
    // Test answer encryption
    const testAnswer = {
      index: 0,
      originalQuestionId: 's3',
      question: 'Instagram password',
      type: 'text',
      answer: 'myInstagramPassword123',
      is_encrypted: false
    };
    
    const encryptedAnswer = await encryptAnswer(testAnswer, '6');
    const decryptedAnswer = await decryptAnswer(encryptedAnswer);
    
    console.log('✅ Answer encryption test passed');
    console.log('Original answer:', testAnswer.answer);
    console.log('Encrypted answer:', encryptedAnswer.answer);
    console.log('Decrypted answer:', decryptedAnswer.answer);
    console.log('Match:', testAnswer.answer === decryptedAnswer.answer);
    
    console.log('🎉 All encryption tests passed!');
  } catch (error) {
    console.error('❌ Encryption test failed:', error);
  }
};
