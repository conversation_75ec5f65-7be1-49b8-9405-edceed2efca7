/**
 * Frontend Encryption Utilities for <PERSON><PERSON><PERSON>
 * Uses Web Crypto API for AES-256-GCM encryption/decryption
 * Compatible with backend encryption format
 */

// Encryption configuration - MUST match backend format
const ALGORITHM = 'AES-GCM';
const KEY_LENGTH = 256;
const IV_LENGTH = 16; // 128 bits for GCM (matches backend)
const SALT_LENGTH = 64; // 512 bits (matches backend)
const TAG_LENGTH = 16; // 128 bits for GCM
const TAG_POSITION = SALT_LENGTH + IV_LENGTH; // Position where tag starts
const ENCRYPTED_POSITION = TAG_POSITION + TAG_LENGTH; // Position where encrypted data starts

/**
 * Get encryption key from environment or generate a session key
 * In production, this should come from a secure key management system
 */
const getEncryptionKey = async (): Promise<string> => {
  // Try to get from environment first
  const envKey = import.meta.env.VITE_ENCRYPTION_KEY;
  if (envKey) {
    return envKey;
  }
  
  // For development, use a consistent key stored in sessionStorage
  let sessionKey = sessionStorage.getItem('heirkey_encryption_key');
  if (!sessionKey) {
    // Generate a new key for this session
    sessionKey = 'heirkey-frontend-encryption-key-' + Date.now();
    sessionStorage.setItem('heirkey_encryption_key', sessionKey);
    console.warn('⚠️ Using session-based encryption key. Set VITE_ENCRYPTION_KEY for production.');
  }
  
  return sessionKey;
};

/**
 * Derive a crypto key from password using PBKDF2
 */
const deriveKey = async (password: string, salt: Uint8Array): Promise<CryptoKey> => {
  const encoder = new TextEncoder();
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(password),
    'PBKDF2',
    false,
    ['deriveBits', 'deriveKey']
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: salt,
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: ALGORITHM, length: KEY_LENGTH },
    false,
    ['encrypt', 'decrypt']
  );
};

/**
 * Convert ArrayBuffer to base64 string
 */
const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

/**
 * Convert base64 string to ArrayBuffer
 */
const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

/**
 * Encrypt a string using AES-256-GCM (compatible with backend format)
 * @param text - The text to encrypt
 * @returns Encrypted string in base64 format: salt(64) + iv(16) + tag(16) + encrypted_data
 */
export const encrypt = async (text: string): Promise<string> => {
  try {
    if (!text || typeof text !== 'string') {
      return text; // Return as-is if not a valid string
    }

    const password = await getEncryptionKey();
    const salt = crypto.getRandomValues(new Uint8Array(SALT_LENGTH));
    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));

    const key = await deriveKey(password, salt);
    const encoder = new TextEncoder();
    const data = encoder.encode(text);

    const encrypted = await crypto.subtle.encrypt(
      {
        name: ALGORITHM,
        iv: iv
      },
      key,
      data
    );

    // Extract the tag from the encrypted result (last 16 bytes for GCM)
    const encryptedArray = new Uint8Array(encrypted);
    const ciphertext = encryptedArray.slice(0, -TAG_LENGTH);
    const tag = encryptedArray.slice(-TAG_LENGTH);

    // Combine salt + iv + tag + encrypted data (matches backend format)
    const combined = new Uint8Array(SALT_LENGTH + IV_LENGTH + TAG_LENGTH + ciphertext.length);
    combined.set(salt, 0);
    combined.set(iv, SALT_LENGTH);
    combined.set(tag, TAG_POSITION);
    combined.set(ciphertext, ENCRYPTED_POSITION);

    return arrayBufferToBase64(combined.buffer);
  } catch (error) {
    console.error('❌ Frontend encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypt a string using AES-256-GCM (compatible with backend format)
 * @param encryptedData - The encrypted data to decrypt
 * @returns Decrypted string
 */
export const decrypt = async (encryptedData: string): Promise<string> => {
  try {
    if (!encryptedData || typeof encryptedData !== 'string') {
      return encryptedData; // Return as-is if not a valid string
    }

    // Check if the data looks like it's encrypted (base64 format)
    if (!isBase64(encryptedData)) {
      return encryptedData; // Return as-is if not base64 (probably not encrypted)
    }

    const password = await getEncryptionKey();
    const combined = new Uint8Array(base64ToArrayBuffer(encryptedData));

    // Extract components (matches backend format: salt + iv + tag + encrypted_data)
    const salt = combined.slice(0, SALT_LENGTH);
    const iv = combined.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
    const tag = combined.slice(TAG_POSITION, ENCRYPTED_POSITION);
    const ciphertext = combined.slice(ENCRYPTED_POSITION);

    const key = await deriveKey(password, salt);

    // Reconstruct the encrypted data with tag for Web Crypto API
    const encryptedWithTag = new Uint8Array(ciphertext.length + tag.length);
    encryptedWithTag.set(ciphertext, 0);
    encryptedWithTag.set(tag, ciphertext.length);

    const decrypted = await crypto.subtle.decrypt(
      {
        name: ALGORITHM,
        iv: iv
      },
      key,
      encryptedWithTag
    );

    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  } catch (error) {
    console.error('❌ Frontend decryption failed:', error);
    // Return original data if decryption fails (might be unencrypted legacy data)
    return encryptedData;
  }
};

/**
 * Check if a string is valid base64
 */
const isBase64 = (str: string): boolean => {
  try {
    return btoa(atob(str)) === str;
  } catch {
    return false;
  }
};

// Types for user input structures - compatible with existing Redux slices
export interface EncryptableAnswer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question?: string;
  type: string;
  answer: string;
  is_encrypted?: boolean;
}

export interface EncryptableSection {
  sectionId?: string;
  originalSectionId: string;
  isCompleted?: boolean;
  answers: EncryptableAnswer[];
}

export interface EncryptableUserInput {
  _id?: string;
  userId: string;
  ownerId?: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: EncryptableSection[];
}

// Import configuration
import { shouldEncryptQuestion as configShouldEncrypt } from '../config/encryptionConfig';

/**
 * Encrypt an answer object if it contains sensitive data
 * @param answer - The answer object to encrypt
 * @param categoryId - Optional category ID for context
 * @returns Answer object with encrypted answer field if sensitive
 */
export const encryptAnswer = async (answer: EncryptableAnswer, categoryId?: string): Promise<EncryptableAnswer> => {
  if (!answer || !answer.answer || answer.is_encrypted) {
    return answer; // Return as-is if no answer or already encrypted
  }

  if (!configShouldEncrypt(answer, categoryId)) {
    return answer; // Don't encrypt non-sensitive questions
  }

  try {
    const encryptedAnswer = await encrypt(answer.answer);
    return {
      ...answer,
      answer: encryptedAnswer,
      is_encrypted: true
    };
  } catch (error) {
    console.error('❌ Failed to encrypt answer:', error);
    return answer; // Return original if encryption fails
  }
};

/**
 * Decrypt an answer object
 * @param answer - The answer object to decrypt
 * @returns Answer object with decrypted answer field
 */
export const decryptAnswer = async (answer: EncryptableAnswer): Promise<EncryptableAnswer> => {
  if (!answer || !answer.answer || !answer.is_encrypted) {
    return answer; // Return as-is if no answer or not encrypted
  }

  try {
    const decryptedAnswer = await decrypt(answer.answer);
    return {
      ...answer,
      answer: decryptedAnswer,
      is_encrypted: false
    };
  } catch (error) {
    console.error('❌ Failed to decrypt answer:', error);
    return answer; // Return original if decryption fails
  }
};

/**
 * Encrypt all sensitive answers in a user input object
 * @param userInput - The user input object
 * @returns User input object with encrypted sensitive answers
 */
export const encryptUserInputAnswers = async (userInput: EncryptableUserInput): Promise<EncryptableUserInput> => {
  if (!userInput || !userInput.answersBySection) {
    return userInput;
  }

  try {
    const encryptedAnswersBySection = await Promise.all(
      userInput.answersBySection.map(async (section) => ({
        ...section,
        answers: section.answers ? await Promise.all(
          section.answers.map(answer => encryptAnswer(answer, userInput.originalCategoryId))
        ) : []
      }))
    );

    return {
      ...userInput,
      answersBySection: encryptedAnswersBySection
    };
  } catch (error) {
    console.error('❌ Failed to encrypt user input answers:', error);
    return userInput; // Return original if encryption fails
  }
};

/**
 * Decrypt all answers in a user input object
 * @param userInput - The user input object
 * @returns User input object with decrypted answers
 */
export const decryptUserInputAnswers = async (userInput: EncryptableUserInput): Promise<EncryptableUserInput> => {
  if (!userInput || !userInput.answersBySection) {
    return userInput;
  }

  try {
    const decryptedAnswersBySection = await Promise.all(
      userInput.answersBySection.map(async (section) => ({
        ...section,
        answers: section.answers ? await Promise.all(
          section.answers.map(answer => decryptAnswer(answer))
        ) : []
      }))
    );

    return {
      ...userInput,
      answersBySection: decryptedAnswersBySection
    };
  } catch (error) {
    console.error('❌ Failed to decrypt user input answers:', error);
    return userInput; // Return original if decryption fails
  }
};

/**
 * Utility function to encrypt an array of user inputs
 * @param userInputs - Array of user input objects
 * @returns Array of user input objects with encrypted answers
 */
export const encryptUserInputsArray = async (userInputs: EncryptableUserInput[]): Promise<EncryptableUserInput[]> => {
  if (!Array.isArray(userInputs)) {
    return userInputs;
  }

  try {
    return await Promise.all(
      userInputs.map(userInput => encryptUserInputAnswers(userInput))
    );
  } catch (error) {
    console.error('❌ Failed to encrypt user inputs array:', error);
    return userInputs;
  }
};

/**
 * Utility function to decrypt an array of user inputs
 * @param userInputs - Array of user input objects
 * @returns Array of user input objects with decrypted answers
 */
export const decryptUserInputsArray = async (userInputs: EncryptableUserInput[]): Promise<EncryptableUserInput[]> => {
  if (!Array.isArray(userInputs)) {
    return userInputs;
  }

  try {
    return await Promise.all(
      userInputs.map(userInput => decryptUserInputAnswers(userInput))
    );
  } catch (error) {
    console.error('❌ Failed to decrypt user inputs array:', error);
    return userInputs;
  }
};
