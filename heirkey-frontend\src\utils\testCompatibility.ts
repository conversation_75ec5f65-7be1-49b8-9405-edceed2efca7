/**
 * Test script to verify frontend-backend encryption compatibility
 * Run this in browser console to test encryption compatibility
 */

import { encrypt, decrypt } from './encryptionUtils';

/**
 * Test encryption compatibility with backend
 * This function can be called from browser console to verify compatibility
 */
export const testEncryptionCompatibility = async () => {
  console.log('🧪 Testing Frontend-Backend Encryption Compatibility...');
  
  try {
    // Test data
    const testCases = [
      'simple password',
      'complex-password-123!@#',
      'Social Security Number: ***********',
      'Bank Account: **********',
      'Very long text that contains sensitive information like passwords, SSN, bank accounts, and other confidential data that should be encrypted properly.',
      '🔐 Unicode test with émojis and spëcial characters',
      '', // Empty string
      '1', // Single character
      'AB', // Two characters
      'ABC' // Three characters (minimum encryption length)
    ];

    console.log('📝 Test Cases:', testCases.length);

    for (let i = 0; i < testCases.length; i++) {
      const originalText = testCases[i];
      console.log(`\n🔍 Test ${i + 1}: "${originalText}"`);

      try {
        // Encrypt
        const encrypted = await encrypt(originalText);
        console.log(`🔒 Encrypted: ${encrypted.substring(0, 50)}${encrypted.length > 50 ? '...' : ''}`);
        
        // Decrypt
        const decrypted = await decrypt(encrypted);
        console.log(`🔓 Decrypted: "${decrypted}"`);
        
        // Verify
        const isMatch = originalText === decrypted;
        console.log(`✅ Match: ${isMatch ? 'PASS' : 'FAIL'}`);
        
        if (!isMatch) {
          console.error(`❌ MISMATCH: Expected "${originalText}", got "${decrypted}"`);
          return false;
        }

        // Test format (should be base64)
        const isBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(encrypted);
        console.log(`📋 Base64 Format: ${isBase64 ? 'PASS' : 'FAIL'}`);
        
        if (originalText && !isBase64) {
          console.error(`❌ Invalid base64 format: ${encrypted}`);
          return false;
        }

        // Test length (encrypted should be longer for non-empty strings)
        if (originalText && encrypted.length <= originalText.length) {
          console.error(`❌ Encrypted length should be longer than original`);
          return false;
        }

      } catch (error) {
        console.error(`❌ Test ${i + 1} failed:`, error);
        return false;
      }
    }

    console.log('\n🎉 All compatibility tests PASSED!');
    console.log('\n📋 Summary:');
    console.log('- Encryption format: salt(64) + iv(16) + tag(16) + encrypted_data');
    console.log('- Algorithm: AES-256-GCM');
    console.log('- Key derivation: PBKDF2 with 100,000 iterations');
    console.log('- Output format: Base64');
    console.log('\n⚠️  Next steps:');
    console.log('1. Ensure backend ENCRYPTION_KEY matches frontend VITE_ENCRYPTION_KEY');
    console.log('2. Test with actual backend API calls');
    console.log('3. Verify data can be decrypted by backend');

    return true;

  } catch (error) {
    console.error('❌ Compatibility test failed:', error);
    return false;
  }
};

/**
 * Test specific encryption scenarios
 */
export const testSensitiveDataEncryption = async () => {
  console.log('🔐 Testing Sensitive Data Encryption...');

  const sensitiveData = [
    { type: 'Password', value: 'mySecretPassword123' },
    { type: 'SSN', value: '***********' },
    { type: 'Bank Account', value: '**********' },
    { type: 'Credit Card', value: '4111-1111-1111-1111' },
    { type: 'PIN', value: '1234' },
    { type: 'API Key', value: 'sk_test_**********abcdef' }
  ];

  for (const data of sensitiveData) {
    console.log(`\n🔍 Testing ${data.type}: ${data.value}`);
    
    try {
      const encrypted = await encrypt(data.value);
      const decrypted = await decrypt(encrypted);
      
      const isMatch = data.value === decrypted;
      console.log(`${isMatch ? '✅' : '❌'} ${data.type}: ${isMatch ? 'PASS' : 'FAIL'}`);
      
      if (!isMatch) {
        console.error(`Expected: "${data.value}", Got: "${decrypted}"`);
        return false;
      }
    } catch (error) {
      console.error(`❌ ${data.type} encryption failed:`, error);
      return false;
    }
  }

  console.log('\n🎉 All sensitive data encryption tests PASSED!');
  return true;
};

/**
 * Test encryption with different key scenarios
 */
export const testKeyCompatibility = async () => {
  console.log('🔑 Testing Key Compatibility...');

  const testText = 'Test encryption with different keys';
  
  try {
    // Test with current key
    const encrypted1 = await encrypt(testText);
    const decrypted1 = await decrypt(encrypted1);
    
    console.log(`🔒 Encryption with current key: ${encrypted1.substring(0, 30)}...`);
    console.log(`🔓 Decryption result: "${decrypted1}"`);
    console.log(`✅ Match: ${testText === decrypted1 ? 'PASS' : 'FAIL'}`);

    if (testText !== decrypted1) {
      console.error('❌ Key compatibility test failed');
      return false;
    }

    console.log('\n🎉 Key compatibility test PASSED!');
    console.log('\n⚠️  Important Notes:');
    console.log('- Frontend VITE_ENCRYPTION_KEY must match backend ENCRYPTION_KEY');
    console.log('- Keys are case-sensitive');
    console.log('- Changing keys will make existing encrypted data unreadable');

    return true;

  } catch (error) {
    console.error('❌ Key compatibility test failed:', error);
    return false;
  }
};

/**
 * Run all compatibility tests
 */
export const runAllCompatibilityTests = async () => {
  console.log('🚀 Running All Encryption Compatibility Tests...\n');

  const tests = [
    { name: 'Basic Compatibility', fn: testEncryptionCompatibility },
    { name: 'Sensitive Data', fn: testSensitiveDataEncryption },
    { name: 'Key Compatibility', fn: testKeyCompatibility }
  ];

  let allPassed = true;

  for (const test of tests) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`🧪 Running ${test.name} Tests`);
    console.log(`${'='.repeat(50)}`);

    const result = await test.fn();
    if (!result) {
      allPassed = false;
      console.error(`❌ ${test.name} tests FAILED`);
    } else {
      console.log(`✅ ${test.name} tests PASSED`);
    }
  }

  console.log(`\n${'='.repeat(50)}`);
  console.log(`🏁 Final Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  console.log(`${'='.repeat(50)}`);

  if (allPassed) {
    console.log('\n🎉 Frontend encryption is compatible with backend format!');
    console.log('\n📋 Next Steps:');
    console.log('1. Set matching encryption keys in both frontend and backend');
    console.log('2. Test with real API calls');
    console.log('3. Verify backend can decrypt frontend-encrypted data');
  } else {
    console.log('\n❌ Please fix the failing tests before proceeding');
  }

  return allPassed;
};

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).testEncryptionCompatibility = testEncryptionCompatibility;
  (window as any).testSensitiveDataEncryption = testSensitiveDataEncryption;
  (window as any).testKeyCompatibility = testKeyCompatibility;
  (window as any).runAllCompatibilityTests = runAllCompatibilityTests;
  
  console.log('🔧 Encryption test functions available in console:');
  console.log('- testEncryptionCompatibility()');
  console.log('- testSensitiveDataEncryption()');
  console.log('- testKeyCompatibility()');
  console.log('- runAllCompatibilityTests()');
}
