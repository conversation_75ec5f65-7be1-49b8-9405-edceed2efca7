import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as Yup from 'yup';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export interface TextQuestion extends BaseQuestion {
  type: 'text';
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
}

export interface NumberQuestion extends BaseQuestion {
  type: 'number';
  placeholder?: string;
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface ChoiceQuestion extends BaseQuestion {
  type: 'choice';
  options: string[];
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
  placeholder?: string;
}

export type Question = TextQuestion | NumberQuestion | BooleanQuestion | ChoiceQuestion | TextareaQuestion;

// Custom form field components for Formik
export const TextareaField = ({ question }: { question: TextQuestion | TextareaQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    helpers.setValue(value);
    helpers.setTouched(true);
  };

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text} {question.required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        id={question.id}
        type="text"
        placeholder={question.placeholder}
        {...field}
        onChange={handleChange}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const NumberField = ({ question }: { question: NumberQuestion }) => {
  const [field, meta] = useField(question.id);

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text} {question.required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        id={question.id}
        type="tel"
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <div className="flex flex-col">
        <Label className="font-medium mb-2" htmlFor={question.id}>
          {question.text} {question.required && <span className="text-red-500">*</span>}
        </Label>
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={() => helpers.setValue('yes')}
            className={field.value === 'yes'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            Yes
          </Button>
          <Button
            type="button"
            onClick={() => helpers.setValue('no')}
            className={field.value === 'no'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            No
          </Button>
        </div>
        {meta.touched && meta.error ? (
          <div className="text-red-500 text-sm mt-1">{meta.error}</div>
        ) : null}
      </div>
    </div>
  );
};

export const ChoiceField = ({ question }: { question: ChoiceQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text} {question.required && <span className="text-red-500">*</span>}
      </Label>
      <Select
        value={field.value || ''}
        onValueChange={(value) => helpers.setValue(value)}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Main component for rendering questions based on condition
export const QuestionItem = ({
  question,
  values
}: {
  question: Question;
  values: Record<string, any>;
}) => {
  // Check if the question should be shown based on dependencies
  const shouldShowQuestion = () => {
    if (!question.dependsOn) return true;
    const { questionId, value } = question.dependsOn;
    return values[questionId] === value;
  };

  if (!shouldShowQuestion()) return null;

  const renderQuestion = (question: Question) => {
    switch (question.type) {
      case 'text':
        return <TextareaField question={question as TextQuestion} />;
      case 'number':
        return <NumberField question={question as NumberQuestion} />;
      case 'boolean':
        return <BooleanField question={question as BooleanQuestion} />;
      case 'choice':
        return <ChoiceField question={question as ChoiceQuestion} />;
      case 'textarea':
        return <TextareaField question={question as TextareaQuestion} />;
      default:
        return null;
    }
  };

  return (
    <div className="p-4 rounded-lg border border-gray-200">
      {renderQuestion(question)}
    </div>
  );
};

// Utility functions
export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach((question) => {
    let fieldSchema;

    switch (question.type) {
      case 'text':
        fieldSchema = Yup.string();
        if (question.validationRules?.minLength) {
          fieldSchema = fieldSchema.min(question.validationRules.minLength);
        }
        if (question.validationRules?.maxLength) {
          fieldSchema = fieldSchema.max(question.validationRules.maxLength);
        }
        break;
      case 'number':
        fieldSchema = Yup.number();
        break;
      case 'boolean':
        fieldSchema = Yup.string().oneOf(['yes', 'no']);
        break;
      case 'choice':
        fieldSchema = Yup.string().oneOf(question.options);
        break;
      default:
        fieldSchema = Yup.string();
    }

    if (question.dependsOn) {
      fieldSchema = fieldSchema.when(question.dependsOn.questionId, {
        is: question.dependsOn.value,
        then: (schema: Yup.StringSchema) => question.required ? schema.required('This field is required') : schema,
        otherwise: (schema: Yup.StringSchema) => schema.notRequired(),
      });
    } else if (question.required) {
      fieldSchema = fieldSchema.required('This field is required');
    }

    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[]) => {
  const initialValues: Record<string, any> = {};
  questions.forEach((question) => {
    initialValues[question.id] = question.answer || '';
  });
  return initialValues;
};

export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: Question[],
  setValues: (values: Record<string, any>) => void
) => {
  const newValues = { ...values };

  questions.forEach((question) => {
    if (question.dependsOn) {
      const { questionId, value } = question.dependsOn;
      if (values[questionId] !== value) {
        newValues[question.id] = '';
      }
    }
  });

  setValues(newValues);
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  const totalQuestions = questions.length;
  const answeredQuestions = questions.filter((question) => {
    if (!question.dependsOn) return values[question.id] !== '';
    const { questionId, value } = question.dependsOn;
    return values[questionId] === value ? values[question.id] !== '' : true;
  }).length;

  return {
    totalQuestions,
    answeredQuestions,
    percentage: Math.round((answeredQuestions / totalQuestions) * 100)
  };
}; 