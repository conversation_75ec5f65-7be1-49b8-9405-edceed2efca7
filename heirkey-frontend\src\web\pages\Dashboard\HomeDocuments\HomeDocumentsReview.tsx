import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import homeDocumentsData from '@/data/homeDocuments.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '@/store/slices/homeDocumentsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInput {
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

// Map section IDs to their routes
const subCategoryRoutes: Record<string, string> = {
  '301': '/category/homedocuments/utility',
  '302': '/category/homedocuments/gas',
  '303': '/category/homedocuments/water',
  '304': '/category/homedocuments/trashcollection',
  '305': '/category/homedocuments/hvac',
  '306': '/category/homedocuments/pest',
  '307': '/category/homedocuments/lawn',
  '308': '/category/homedocuments/cable',
  '309': '/category/homedocuments/internet',
};

// Map question IDs to their section IDs
const questionToSubcategoryMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(homeDocumentsData).forEach(([subcategoryId, questions]) => {
  questions.forEach((question: any) => {
    questionToSubcategoryMap[question.id] = subcategoryId;
  });
});


export default function HomeDocumentsReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  // Get data from Redux store
  const userInputs = useAppSelector((state: any) => state.homeDocuments.userInputs) as ReduxUserInput[];
  const isLoading = useAppSelector((state: any) => state.homeDocuments.loading);
  const error = useAppSelector((state: any) => state.homeDocuments.error);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: user?.image || avatar
  };

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in HomeDocumentsReview component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in HomeDocumentsReview component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs to create topics for review page
  useEffect(() => {    
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: ReduxUserInput) => {        
        userInput.answersBySection.forEach((section) => {
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;
            const subcategoryId = questionToSubcategoryMap[questionId];
            const allQuestions = homeDocumentsData[subcategoryId as keyof typeof homeDocumentsData];
            const questionData = allQuestions?.find((q: any) => q.id === questionId);

            if (questionData) {
              let displayAnswer = answer.answer;
              
              // Handle choice type answers
              if (questionData.type === 'choice') {
                // If the answer is "Other (Please List)", look for the other provider name
                if (answer.answer === "Other (Please List)") {
                  const otherAnswer = section.answers.find(a => a.originalQuestionId === `${answer.originalQuestionId}_other`);
                  if (otherAnswer) {
                    displayAnswer = `Other: ${otherAnswer.answer}`;
                  }
                }
              }

              allTopics.push({
                id: questionId,
                title: questionData.text,
                subtitle: `Section: ${section.originalSectionId}`,
                data: displayAnswer,
                onEdit: () => {
                  // Navigate to the appropriate section page with question ID as a parameter
                  const route = subCategoryRoutes[subcategoryId];
                  if (route) {
                    navigate(`${route}?questionId=${questionId}`);
                  }
                }
              });
            }
          });
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    console.error('Error in HomeDocumentsReview:', error);
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user || !user.id) {
    console.warn('No user found in HomeDocumentsReview');
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  if (userInputs.length === 0 && !isLoading) {
    console.warn('No user inputs found in HomeDocumentsReview');
    return <div className="flex justify-center items-center h-screen">No home documents answers found. Please complete some questions first.</div>;
  }

  return (
    <div className="flex flex-col items-center">
    <CategoryReviewPage
      categoryTitle="Home Documents"
      infoTitle="How to edit your information"
      infoDescription="Now, you are about to enter details about your home documents, preferences, and essential information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
      topics={topics}
      user={userInfo}
      onPrint={() => window.print()}
      afterTopics={
        <button
          onClick={() => navigate('/category/willinstructions')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to Will Instructions
        </button>
      }
    />
    </div>
  );
}
