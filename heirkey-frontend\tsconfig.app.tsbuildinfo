{"root": ["./src/app.tsx", "./src/global.d.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/requestcategories/requestcategorydialog.tsx", "./src/components/requestcategories/requestlist.tsx", "./src/components/auth/googleauthcallback.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/common/acceptinvitation.tsx", "./src/components/common/approvecategoryrequest.tsx", "./src/components/common/invitedialog.tsx", "./src/components/subscription/subscribepage.tsx", "./src/components/ui/circularprogress.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/list.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/config/encryptionconfig.ts", "./src/constants/categories.ts", "./src/contexts/authcontext.tsx", "./src/data/categorytabsconfig.ts", "./src/hooks/use-toast.ts", "./src/lib/utils.ts", "./src/mobile/components/auth/emailverificationform.tsx", "./src/mobile/components/auth/forgetpassword.tsx", "./src/mobile/components/auth/loginform.tsx", "./src/mobile/components/auth/registerform.tsx", "./src/mobile/components/auth/resetpassword.tsx", "./src/mobile/components/auth/userprofile.tsx", "./src/mobile/components/category/categoryreviewpage.tsx", "./src/mobile/components/dashboard/categorycard.tsx", "./src/mobile/components/dashboard/funeralinstructions/formfields.tsx", "./src/mobile/components/dashboard/funeralinstructions/scrolltoquestion.tsx", "./src/mobile/components/dashboard/homedocuments/formfields.tsx", "./src/mobile/components/dashboard/homedocuments/scrolltoquestion.tsx", "./src/mobile/components/dashboard/homeinstructions/formfields.tsx", "./src/mobile/components/dashboard/homeinstructions/scrolltoquestion.tsx", "./src/mobile/components/dashboard/importantcontacts/formfields.tsx", "./src/mobile/components/dashboard/importantcontacts/scrolltoquestion.tsx", "./src/mobile/components/dashboard/socialmedia/formfields.tsx", "./src/mobile/components/dashboard/socialmedia/scrolltoquestion.tsx", "./src/mobile/components/dashboard/socialmedia/types.ts", "./src/mobile/components/dashboard/willlocation/formfields.tsx", "./src/mobile/components/header/gradiantheader.tsx", "./src/mobile/components/layout/footer.tsx", "./src/mobile/components/layout/header.tsx", "./src/mobile/components/layout/layout.tsx", "./src/mobile/components/layout/useravatar.tsx", "./src/mobile/components/subscription/mobilesubscribepage.tsx", "./src/mobile/pages/landingpage.tsx", "./src/mobile/pages/authpages/emailverifypage.tsx", "./src/mobile/pages/authpages/loginpage.tsx", "./src/mobile/pages/authpages/registerpage.tsx", "./src/mobile/pages/authpages/userpofile.tsx", "./src/mobile/pages/confirmationpage/categoryconfirmpage.tsx", "./src/mobile/pages/dashboard/dashboardpage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/ceremonylocationpage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/funeralarrangementreviewpage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/funeralarrangementspage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/funeralclergypage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/funeraldetailspage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/funeralnotificationpage.tsx", "./src/mobile/pages/dashboard/funeralarrangements/funeralproceedingspage.tsx", "./src/mobile/pages/dashboard/homedocuments/cablepage.tsx", "./src/mobile/pages/dashboard/homedocuments/gaspage.tsx", "./src/mobile/pages/dashboard/homedocuments/homedocumentspage.tsx", "./src/mobile/pages/dashboard/homedocuments/homedocumentsreviewpage.tsx", "./src/mobile/pages/dashboard/homedocuments/hvacpage.tsx", "./src/mobile/pages/dashboard/homedocuments/internetpage.tsx", "./src/mobile/pages/dashboard/homedocuments/lawnpage.tsx", "./src/mobile/pages/dashboard/homedocuments/pestpage.tsx", "./src/mobile/pages/dashboard/homedocuments/trashpage.tsx", "./src/mobile/pages/dashboard/homedocuments/utilitypage.tsx", "./src/mobile/pages/dashboard/homedocuments/waterpage.tsx", "./src/mobile/pages/dashboard/homeinstructions/homeinstructionspage.tsx", "./src/mobile/pages/dashboard/homeinstructions/homeinstructionsreviewpage.tsx", "./src/mobile/pages/dashboard/homeinstructions/homelocationinstructionspage.tsx", "./src/mobile/pages/dashboard/homeinstructions/otherinstructionspage.tsx", "./src/mobile/pages/dashboard/homeinstructions/petsinstructionspage.tsx", "./src/mobile/pages/dashboard/homeinstructions/securityinstructionspage.tsx", "./src/mobile/pages/dashboard/homeinstructions/trashinstructionspage.tsx", "./src/mobile/pages/dashboard/importantcontacts/clubspage.tsx", "./src/mobile/pages/dashboard/importantcontacts/contactpage.tsx", "./src/mobile/pages/dashboard/importantcontacts/contactreviewpage.tsx", "./src/mobile/pages/dashboard/importantcontacts/friendsandfamily.tsx", "./src/mobile/pages/dashboard/importantcontacts/religiousaffiliation.tsx", "./src/mobile/pages/dashboard/importantcontacts/workpage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/cellphonepage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/emailpage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/facebookpage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/instagrampage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/otheraccountspage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/socialmediaphonepage.tsx", "./src/mobile/pages/dashboard/socialmediaphone/socialmediaphonereviewpage.tsx", "./src/mobile/pages/dashboard/willlocation/legalinstructionspage.tsx", "./src/mobile/pages/dashboard/willlocation/locationinstrucationspage.tsx", "./src/mobile/pages/dashboard/willlocation/willinstructionreviewpage.tsx", "./src/mobile/pages/dashboard/willlocation/willinstructionspage.tsx", "./src/mobile/pages/global/splashpage.tsx", "./src/mobile/utils/questionutils.ts", "./src/services/api.ts", "./src/services/authservice.ts", "./src/services/inviteservice.ts", "./src/services/ownerservice.ts", "./src/services/requestedcategoriesservice.ts", "./src/services/subscriptionservice.ts", "./src/services/userinputservice.ts", "./src/store/hooks.ts", "./src/store/index.ts", "./src/store/slices/funeralarrangementsslice.ts", "./src/store/slices/homedocumentsslice.ts", "./src/store/slices/homeinstructionsslice.ts", "./src/store/slices/importantcontactsslice.ts", "./src/store/slices/inviteslice.ts", "./src/store/slices/requestedcategoriesslice.ts", "./src/store/slices/socialmediaslice.ts", "./src/store/slices/subscriptionslice.ts", "./src/store/slices/willinstructionsslice.ts", "./src/types/invite.types.ts", "./src/types/owner.ts", "./src/types/react-select-country-list.d.ts", "./src/types/requestedcategories.ts", "./src/types/subscription.ts", "./src/utils/encryptionutils.ts", "./src/utils/ownerutils.ts", "./src/utils/testcompatibility.ts", "./src/web/weblandingpage.tsx", "./src/web/components/category/categoryreviewpage.tsx", "./src/web/components/category/funeralinstructions/formfields.tsx", "./src/web/components/category/funeralinstructions/scrolltoquestion.tsx", "./src/web/components/category/homedocuments/formfields.tsx", "./src/web/components/category/homedocuments/scrolltoquestion.tsx", "./src/web/components/category/homeinstructions/formfields.tsx", "./src/web/components/category/homeinstructions/scrolltoquestion.tsx", "./src/web/components/category/importantcontacts/formfields.tsx", "./src/web/components/category/importantcontacts/scrolltoquestion.tsx", "./src/web/components/category/socialmedia/formfields.tsx", "./src/web/components/category/socialmedia/scrolltoquestion.tsx", "./src/web/components/category/willinstructions/formfields.tsx", "./src/web/components/category/willinstructions/scrolltoquestion.tsx", "./src/web/components/global/goodtoknowbox.tsx", "./src/web/components/global/subcategoryfooternav.tsx", "./src/web/components/global/subcategoryheader.tsx", "./src/web/components/global/subcategorytabs.tsx", "./src/web/components/global/subcategorytitle.tsx", "./src/web/components/layout/appheader.tsx", "./src/web/components/layout/footer.tsx", "./src/web/components/layout/landingheader.tsx", "./src/web/components/layout/subheader.tsx", "./src/web/components/layout/weblayout.tsx", "./src/web/components/auth/webauthform.tsx", "./src/web/components/auth/webforgetpassword.tsx", "./src/web/components/auth/webresetpassword.tsx", "./src/web/components/auth/webuserprofile.tsx", "./src/web/components/auth/webverificationform.tsx", "./src/web/pages/authpages/weblogin.tsx", "./src/web/pages/authpages/webregister.tsx", "./src/web/pages/dashboard/dashboard.tsx", "./src/web/pages/dashboard/funeralarrangements/ceremonylocation.tsx", "./src/web/pages/dashboard/funeralarrangements/funeralarrangementreview.tsx", "./src/web/pages/dashboard/funeralarrangements/funeralarrangements.tsx", "./src/web/pages/dashboard/funeralarrangements/funeralclergy.tsx", "./src/web/pages/dashboard/funeralarrangements/funeraldetails.tsx", "./src/web/pages/dashboard/funeralarrangements/funeralnotification.tsx", "./src/web/pages/dashboard/funeralarrangements/funeralproceedings.tsx", "./src/web/pages/dashboard/homedocuments/cable.tsx", "./src/web/pages/dashboard/homedocuments/gas.tsx", "./src/web/pages/dashboard/homedocuments/hvac.tsx", "./src/web/pages/dashboard/homedocuments/homedocuments.tsx", "./src/web/pages/dashboard/homedocuments/homedocumentsreview.tsx", "./src/web/pages/dashboard/homedocuments/internet.tsx", "./src/web/pages/dashboard/homedocuments/lawncare.tsx", "./src/web/pages/dashboard/homedocuments/pest.tsx", "./src/web/pages/dashboard/homedocuments/trashcollection.tsx", "./src/web/pages/dashboard/homedocuments/utility.tsx", "./src/web/pages/dashboard/homedocuments/water.tsx", "./src/web/pages/dashboard/homeinstructions/homeinstructions.tsx", "./src/web/pages/dashboard/homeinstructions/homeinstructionsreview.tsx", "./src/web/pages/dashboard/homeinstructions/homelocationinstructions.tsx", "./src/web/pages/dashboard/homeinstructions/otherinstructions.tsx", "./src/web/pages/dashboard/homeinstructions/petsinstructions.tsx", "./src/web/pages/dashboard/homeinstructions/securityinstructions.tsx", "./src/web/pages/dashboard/homeinstructions/trashinstructions.tsx", "./src/web/pages/dashboard/importantcontact/clubsinstructions.tsx", "./src/web/pages/dashboard/importantcontact/friendsfamily.tsx", "./src/web/pages/dashboard/importantcontact/importantcontact.tsx", "./src/web/pages/dashboard/importantcontact/importantcontactreview.tsx", "./src/web/pages/dashboard/importantcontact/religiousaffiliation.tsx", "./src/web/pages/dashboard/importantcontact/workinstructions.tsx", "./src/web/pages/dashboard/socialmedia/cellphone.tsx", "./src/web/pages/dashboard/socialmedia/email.tsx", "./src/web/pages/dashboard/socialmedia/facebook.tsx", "./src/web/pages/dashboard/socialmedia/instagram.tsx", "./src/web/pages/dashboard/socialmedia/othersocial.tsx", "./src/web/pages/dashboard/socialmedia/socialmedia.tsx", "./src/web/pages/dashboard/socialmedia/socialmediareview.tsx", "./src/web/pages/dashboard/willinstructions/legalinstructions.tsx", "./src/web/pages/dashboard/willinstructions/locationinstructions.tsx", "./src/web/pages/dashboard/willinstructions/willinstructions.tsx", "./src/web/pages/dashboard/willinstructions/willinstructionsreview.tsx", "./src/web/pages/global/categorystartup.tsx", "./src/web/pages/global/searchpanel.tsx", "./src/web/pages/global/websplashpage.tsx", "./src/web/pages/landingpage/contactsection.tsx", "./src/web/pages/landingpage/featuresection.tsx", "./src/web/pages/landingpage/herosection.tsx", "./src/web/pages/landingpage/howitworkssection.tsx", "./src/web/pages/landingpage/testimonialsection.tsx"], "version": "5.7.3"}